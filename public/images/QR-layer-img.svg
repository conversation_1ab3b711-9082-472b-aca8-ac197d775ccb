<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="291.938" height="589.396" viewBox="0 0 291.938 589.396">
  <defs>
    <clipPath id="clip-path">
      <path id="Screen_mask" data-name="Screen mask" d="M291.938,589.4H0V0H291.938V589.4ZM53.564,14.494a36.969,36.969,0,0,0-37,37V537.9a36.97,36.97,0,0,0,37,37h184.81a36.969,36.969,0,0,0,37-37V51.494a36.966,36.966,0,0,0-37-37H208.112a4.015,4.015,0,0,0-2.346,3.546V15.913a23,23,0,0,1-23,22.806H113.551a23,23,0,0,1-23-23v2.448a4.058,4.058,0,0,0-2.357-3.674Z" transform="translate(0 0)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.5" y1="0.006" x2="0.5" y2="0.991" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#24262b"/>
      <stop offset="0.07" stop-color="#5a5c60"/>
      <stop offset="0.185" stop-color="#313338"/>
      <stop offset="0.824" stop-color="#37393e"/>
      <stop offset="0.931" stop-color="#5a5c60"/>
      <stop offset="1" stop-color="#424345"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" y1="0.006" x2="0.5" y2="0.991" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#24262b"/>
      <stop offset="0.04" stop-color="#5a5c60"/>
      <stop offset="0.111" stop-color="#313338"/>
      <stop offset="0.894" stop-color="#37393e"/>
      <stop offset="0.958" stop-color="#5a5c60"/>
      <stop offset="1" stop-color="#424345"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" y1="0.006" x2="0.5" y2="0.991" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#24262b"/>
      <stop offset="0.022" stop-color="#5a5c60"/>
      <stop offset="0.057" stop-color="#313338"/>
      <stop offset="0.923" stop-color="#37393e"/>
      <stop offset="0.983" stop-color="#5a5c60"/>
      <stop offset="1" stop-color="#424345"/>
    </linearGradient>
    <filter id="Inner_glow">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur"/>
      <feFlood flood-color="#fff" flood-opacity="0.251" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-5" x1="0.845" y1="0.848" x2="0.188" y2="0.114" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1d1e20"/>
      <stop offset="1" stop-color="#08090a"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="0.5" cy="0.5" r="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0a1629"/>
      <stop offset="0.442" stop-color="#0a1629"/>
      <stop offset="1"/>
    </radialGradient>
    <filter id="Oval_2" x="98.453" y="17.705" width="12.211" height="12.211" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur-2"/>
      <feFlood flood-opacity="0.502"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-6" y1="0.5" x2="0.397" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#377ab7"/>
      <stop offset="1" stop-color="#377ab7" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="1" y1="0.5" x2="0.656" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#173481" stop-opacity="0.835"/>
      <stop offset="1" stop-color="#377ab7" stop-opacity="0"/>
    </linearGradient>
    <filter id="Oval_2-2">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur-3"/>
      <feFlood flood-color="#97c2ff" result="color-2"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-3"/>
      <feComposite operator="in" in="color-2"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="Oval_2-3">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-4"/>
      <feFlood result="color-3"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-4"/>
      <feComposite operator="in" in="color-3"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="Speaker" x="122.158" y="10.024" width="47.621" height="4.451" filterUnits="userSpaceOnUse">
      <feOffset dy="-1" input="SourceAlpha"/>
      <feGaussianBlur result="blur-5"/>
      <feFlood flood-color="#fff" flood-opacity="0.051"/>
      <feComposite operator="in" in2="blur-5"/>
    </filter>
    <filter id="Speaker-2" x="122.158" y="11.024" width="47.621" height="4.451" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur result="blur-6"/>
      <feFlood flood-color="#fff" flood-opacity="0.051"/>
      <feComposite operator="in" in2="blur-6"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Speaker-3" x="122.158" y="11.024" width="47.621" height="4.451" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-7"/>
      <feFlood flood-opacity="0.502" result="color-4"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-7"/>
      <feComposite operator="in" in="color-4"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="Speaker-4">
      <feOffset dx="0.5" input="SourceAlpha"/>
      <feGaussianBlur result="blur-8"/>
      <feFlood flood-color="#fff" flood-opacity="0.2" result="color-5"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-8"/>
      <feComposite operator="in" in="color-5"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="Speaker-5">
      <feOffset dx="-0.5" input="SourceAlpha"/>
      <feGaussianBlur result="blur-9"/>
      <feFlood flood-color="#fff" flood-opacity="0.2" result="color-6"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-9"/>
      <feComposite operator="in" in="color-6"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_43763" data-name="Group 43763" transform="translate(-7702 -10471.604)">
    <rect id="Mockup" width="262" height="562" rx="38" transform="translate(7718 10484.999)" fill="#fff"/>
    <path id="Screen_mask-2" data-name="Screen mask" d="M0,0H291V587H0Z" transform="translate(7702 10471.604)" fill="none"/>
    <g id="Phone" transform="translate(7702 10471.604)" clip-path="url(#clip-path)">
      <g id="Buttons_mute_switch" data-name="Buttons + mute switch" transform="translate(0 92.826)">
        <g id="Rectangle">
          <g id="Rectangle-2" data-name="Rectangle" stroke="rgba(0,0,0,0.12)" stroke-width="0.75" fill="url(#linear-gradient)">
            <rect width="3.451" height="24.846" rx="1" stroke="none"/>
            <rect x="0.375" y="0.375" width="2.701" height="24.096" rx="0.625" fill="none"/>
          </g>
          <g id="Rectangle-3" data-name="Rectangle" fill="none" stroke="#707070" stroke-width="0.5">
            <rect width="3.451" height="24.846" rx="1" stroke="none"/>
            <rect x="0.25" y="0.25" width="2.951" height="24.346" rx="0.75" fill="none"/>
          </g>
        </g>
        <g id="Rectangle-4" data-name="Rectangle" transform="translate(0.345 46.586)">
          <g id="Rectangle-5" data-name="Rectangle" stroke="rgba(0,0,0,0.12)" stroke-width="0.75" fill="url(#linear-gradient-2)">
            <rect width="3.451" height="49.691" rx="1" stroke="none"/>
            <rect x="0.375" y="0.375" width="2.701" height="48.941" rx="0.625" fill="none"/>
          </g>
          <g id="Rectangle-6" data-name="Rectangle" fill="none" stroke="#707070" stroke-width="0.5">
            <rect width="3.451" height="49.691" rx="1" stroke="none"/>
            <rect x="0.25" y="0.25" width="2.951" height="49.191" rx="0.75" fill="none"/>
          </g>
        </g>
        <g id="Rectangle-7" data-name="Rectangle" transform="translate(0.345 110.08)">
          <g id="Rectangle-8" data-name="Rectangle" stroke="rgba(0,0,0,0.12)" stroke-width="0.75" fill="url(#linear-gradient-2)">
            <rect width="3.451" height="49.691" rx="1" stroke="none"/>
            <rect x="0.375" y="0.375" width="2.701" height="48.941" rx="0.625" fill="none"/>
          </g>
          <g id="Rectangle-9" data-name="Rectangle" fill="none" stroke="#707070" stroke-width="0.5">
            <rect width="3.451" height="49.691" rx="1" stroke="none"/>
            <rect x="0.25" y="0.25" width="2.951" height="49.191" rx="0.75" fill="none"/>
          </g>
        </g>
        <g id="Rectangle-10" data-name="Rectangle" transform="translate(288.141 63.84)">
          <g id="Rectangle-11" data-name="Rectangle" stroke="rgba(0,0,0,0.12)" stroke-width="0.75" fill="url(#linear-gradient-4)">
            <rect width="3.451" height="78.678" rx="1" stroke="none"/>
            <rect x="0.375" y="0.375" width="2.701" height="77.928" rx="0.625" fill="none"/>
          </g>
          <g id="Rectangle-12" data-name="Rectangle" fill="none" stroke="#707070" stroke-width="0.5">
            <rect width="3.451" height="78.678" rx="1" stroke="none"/>
            <rect x="0.25" y="0.25" width="2.951" height="78.178" rx="0.75" fill="none"/>
          </g>
        </g>
      </g>
      <g id="Bezel" transform="translate(2.07 0)">
        <g id="Bezel-2" data-name="Bezel" stroke="#0e0f11" stroke-width="7">
          <rect width="287.796" height="589.396" rx="48" stroke="none"/>
          <rect x="3.5" y="3.5" width="280.796" height="582.396" rx="44.5" fill="none"/>
        </g>
        <g id="Bezel-3" data-name="Bezel" fill="none" stroke="#707070" stroke-width="6">
          <rect width="287.796" height="589.396" rx="48" stroke="none"/>
          <rect x="3" y="3" width="281.796" height="583.396" rx="45" fill="none"/>
        </g>
        <g id="Bezel-4" data-name="Bezel" fill="none" stroke="#54575a" stroke-width="2.6">
          <rect width="287.796" height="589.396" rx="48" stroke="none"/>
          <rect x="1.3" y="1.3" width="285.196" height="586.796" rx="46.7" fill="none"/>
        </g>
        <g id="Bezel-5" data-name="Bezel" fill="none" stroke="#414447" stroke-width="1.8">
          <rect width="287.796" height="589.396" rx="48" stroke="none"/>
          <rect x="0.9" y="0.9" width="285.996" height="587.596" rx="47.1" fill="none"/>
        </g>
      </g>
      <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Inner_glow)">
        <rect id="Inner_glow-2" data-name="Inner glow" width="285.036" height="586.635" rx="52" transform="translate(3.45 1.38)" fill="#fff"/>
      </g>
      <g id="Antenna_lines" data-name="Antenna lines" transform="translate(2.07 0)">
        <g id="Top">
          <rect id="Rectangle-13" data-name="Rectangle" width="4.141" height="4.141" transform="translate(0 62.114)" fill="rgba(96,101,128,0.8)"/>
          <rect id="Rectangle-14" data-name="Rectangle" width="4.141" height="4.141" transform="translate(283.655 62.114)" fill="rgba(96,101,128,0.8)"/>
          <rect id="Rectangle-15" data-name="Rectangle" width="4.141" height="4.141" transform="translate(223.611)" fill="rgba(96,101,128,0.8)"/>
        </g>
        <g id="Bottom" transform="translate(0 523.14)">
          <rect id="Rectangle-16" data-name="Rectangle" width="4.141" height="4.141" transform="translate(60.044 62.114)" fill="rgba(96,101,128,0.8)"/>
          <rect id="Rectangle-17" data-name="Rectangle" width="4.141" height="4.141" fill="rgba(96,101,128,0.8)"/>
          <rect id="Rectangle-18" data-name="Rectangle" width="4.141" height="4.141" transform="translate(283.655)" fill="rgba(96,101,128,0.8)"/>
        </g>
      </g>
      <g id="Sensor_Housing" data-name="Sensor Housing" transform="translate(99.038 11.023)">
        <g id="Camera" transform="translate(0 7.266)">
          <circle id="Oval" cx="5.521" cy="5.521" r="5.521" transform="translate(0 0)" fill="url(#linear-gradient-5)"/>
          <g id="Oval_2-4" data-name="Oval 2" transform="translate(2.416 2.416)">
            <g transform="matrix(1, 0, 0, 1, -101.45, -20.7)" filter="url(#Oval_2)">
              <circle id="Oval_2-5" data-name="Oval 2" cx="3.106" cy="3.106" r="3.106" transform="translate(101.45 20.7)" fill="url(#radial-gradient)"/>
            </g>
            <circle id="Oval_2-6" data-name="Oval 2" cx="3.106" cy="3.106" r="3.106" fill="url(#linear-gradient-6)"/>
            <circle id="Oval_2-7" data-name="Oval 2" cx="3.106" cy="3.106" r="3.106" fill="url(#linear-gradient-7)"/>
          </g>
          <g id="Oval_2-8" data-name="Oval 2" transform="translate(2.416 2.416)">
            <circle id="Oval_2-9" data-name="Oval 2" cx="3.106" cy="3.106" r="3.106" fill="none"/>
            <circle id="Oval_2-10" data-name="Oval 2" cx="3.106" cy="3.106" r="3.106" fill="none"/>
            <g transform="matrix(1, 0, 0, 1, -101.45, -20.7)" filter="url(#Oval_2-2)">
              <circle id="Oval_2-11" data-name="Oval 2" cx="3.106" cy="3.106" r="3.106" transform="translate(101.45 20.7)" fill="#fff"/>
            </g>
            <g transform="matrix(1, 0, 0, 1, -101.45, -20.7)" filter="url(#Oval_2-3)">
              <circle id="Oval_2-12" data-name="Oval 2" cx="3.106" cy="3.106" r="3.106" transform="translate(101.45 20.7)" fill="#fff"/>
            </g>
          </g>
          <circle id="Oval-2" data-name="Oval" cx="0.69" cy="0.69" r="0.69" transform="translate(3.936 4.278)" fill="#493b7a" style="mix-blend-mode: screen;isolation: isolate"/>
          <circle id="Oval-3" data-name="Oval" cx="0.69" cy="0.69" r="0.69" transform="translate(5.976 5.804)" fill="#3c657f" style="mix-blend-mode: screen;isolation: isolate"/>
        </g>
        <g id="Speaker-6" data-name="Speaker" transform="translate(23.12)" opacity="0.4">
          <g transform="matrix(1, 0, 0, 1, -122.16, -11.02)" filter="url(#Speaker)">
            <rect id="Speaker-7" data-name="Speaker" width="47.621" height="3.451" rx="1.725" transform="translate(122.16 11.02)" fill="#fff"/>
          </g>
          <g data-type="innerShadowGroup">
            <g transform="matrix(1, 0, 0, 1, -122.16, -11.02)" filter="url(#Speaker-2)">
              <rect id="Speaker-8" data-name="Speaker" width="47.621" height="3.451" rx="1.725" transform="translate(122.16 11.02)" fill="#1a1a1a"/>
            </g>
            <g transform="matrix(1, 0, 0, 1, -122.16, -11.02)" filter="url(#Speaker-3)">
              <rect id="Speaker-9" data-name="Speaker" width="47.621" height="3.451" rx="1.725" transform="translate(122.16 11.02)" fill="#fff"/>
            </g>
          </g>
          <g transform="matrix(1, 0, 0, 1, -122.16, -11.02)" filter="url(#Speaker-4)">
            <rect id="Speaker-10" data-name="Speaker" width="47.621" height="3.451" rx="1.725" transform="translate(122.16 11.02)" fill="#fff"/>
          </g>
          <g transform="matrix(1, 0, 0, 1, -122.16, -11.02)" filter="url(#Speaker-5)">
            <rect id="Speaker-11" data-name="Speaker" width="47.621" height="3.451" rx="1.725" transform="translate(122.16 11.02)" fill="#fff"/>
          </g>
          <rect id="Speaker-12" data-name="Speaker" width="47.621" height="3.451" rx="1.725" fill="none"/>
        </g>
      </g>
    </g>
    <rect id="Bars_Status_Bar_iPhone_Light_background" data-name="Bars / Status Bar / iPhone / Light background" width="273.822" height="32.415" transform="translate(7702 10483.029)" fill="none"/>
    <rect id="Bars_Status_Bar_iPhone_Light_background-2" data-name="Bars / Status Bar / iPhone / Light background" width="256.967" height="30.151" transform="translate(7713.339 10484.999)" fill="none"/>
    <g id="Border" transform="translate(7943.811 10496.875)" fill="none" stroke="#fff" stroke-width="1" opacity="0.35">
      <rect width="15.075" height="7.766" rx="2.667" stroke="none"/>
      <rect x="0.5" y="0.5" width="14.075" height="6.766" rx="2.167" fill="none"/>
    </g>
    <path id="Cap" d="M0,0V2.741A1.487,1.487,0,0,0,.91,1.37,1.487,1.487,0,0,0,0,0" transform="translate(7959.571 10499.388)" fill="#fff" opacity="0.4"/>
    <rect id="Capacity" width="12.334" height="5.025" rx="1.333" transform="translate(7945.181 10498.246)" fill="#fff"/>
    <path id="Wifi-path" d="M5.254,1.565A5.947,5.947,0,0,1,9.349,3.2a.212.212,0,0,0,.3,0l.8-.8a.221.221,0,0,0,0-.313,7.5,7.5,0,0,0-10.375,0,.221.221,0,0,0,0,.313l.8.8a.212.212,0,0,0,.3,0,5.948,5.948,0,0,1,4.1-1.635Z" transform="translate(7929.877 10496.875)" fill="#fff"/>
    <path id="Wifi-path-2" data-name="Wifi-path" d="M3.422,1.567a3.378,3.378,0,0,1,2.266.873.214.214,0,0,0,.3,0l.794-.8a.221.221,0,0,0,0-.316,4.929,4.929,0,0,0-6.709,0,.221.221,0,0,0,0,.316l.794.8a.214.214,0,0,0,.3,0,3.378,3.378,0,0,1,2.265-.873Z" transform="translate(7931.709 10499.481)" fill="#fff"/>
    <path id="Wifi-path-3" data-name="Wifi-path" d="M3.118.87A.216.216,0,0,0,3.112.556a2.356,2.356,0,0,0-3.04,0A.216.216,0,0,0,.065.87L1.439,2.256a.215.215,0,0,0,.305,0Z" transform="translate(7933.54 10502.093)" fill="#fff"/>
    <path id="Cellular-Connection-path" d="M.685,0H1.37a.685.685,0,0,1,.685.685v1.37a.685.685,0,0,1-.685.685H.685A.685.685,0,0,1,0,2.056V.685A.685.685,0,0,1,.685,0Z" transform="translate(7914.802 10501.674)" fill="#fff"/>
    <path id="Cellular-Connection-path-2" data-name="Cellular-Connection-path" d="M.685,0H1.37a.685.685,0,0,1,.685.685V3.426a.685.685,0,0,1-.685.685H.685A.685.685,0,0,1,0,3.426V.685A.685.685,0,0,1,.685,0Z" transform="translate(7917.999 10500.302)" fill="#fff"/>
    <path id="Cellular-Connection-path-3" data-name="Cellular-Connection-path" d="M.685,0H1.37a.685.685,0,0,1,.685.685v4.34a.685.685,0,0,1-.685.685H.685A.685.685,0,0,1,0,5.025V.685A.685.685,0,0,1,.685,0Z" transform="translate(7921.197 10498.704)" fill="#fff"/>
    <path id="Cellular-Connection-path-4" data-name="Cellular-Connection-path" d="M.685,0H1.37a.685.685,0,0,1,.685.685V6.624a.685.685,0,0,1-.685.685H.685A.685.685,0,0,1,0,6.624V.685A.685.685,0,0,1,.685,0Z" transform="translate(7924.396 10497.105)" fill="#fff"/>
    <rect id="Bars_Status_Bar_iPhone_x_Time_-_Light_background" data-name="Bars / Status Bar / iPhone / x / Time - Light background" width="37.003" height="14.39" transform="translate(7727.729 10489.795)" fill="none"/>
    <text id="_9:41" data-name="9:41" transform="translate(7759 10506)" fill="#fff" font-size="11" font-family="SegoeUISymbol, Segoe UI Symbol" letter-spacing="0.006em"><tspan x="-10" y="0">9:41</tspan></text>
    <rect id="Home_Indicator" data-name="Home Indicator" width="111" height="3" rx="1.5" transform="translate(7794 11039.604)" fill="#fff" opacity="0.51"/>
  </g>
</svg>
