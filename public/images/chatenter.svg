<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_671_667)">
<path d="M20.8293 1.15893C20.6741 1.00814 20.4458 0.960051 20.2439 1.03451L3.36874 7.22188C3.15503 7.30047 3.0095 7.49986 3.00047 7.72725C2.99118 7.95463 3.12001 8.16531 3.32679 8.26087L10.4687 11.5571L14.3172 18.7042C14.4157 18.8874 14.6067 19 14.8121 19C14.8283 19 14.8445 18.9995 14.8607 18.9979C15.0843 18.9787 15.2752 18.8284 15.346 18.6155L20.9711 1.74088C21.0395 1.5356 20.9844 1.3096 20.8292 1.15905L20.8293 1.15893Z" fill="url(#paint0_linear_671_667)"/>
</g>
<defs>
<filter id="filter0_d_671_667" x="0" y="0" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_671_667"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_671_667" result="shape"/>
</filter>
<linearGradient id="paint0_linear_671_667" x1="12" y1="1" x2="12" y2="19" gradientUnits="userSpaceOnUse">
<stop stop-color="#38568F"/>
<stop offset="1" stop-color="#38A1F7"/>
</linearGradient>
</defs>
</svg>
