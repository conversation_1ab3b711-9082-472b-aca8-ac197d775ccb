<!DOCTYPE html>
<html lang="en">

<head>
  <!-- microsoft clarity code for ui portals /> -->
  <!-- <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "n68lxmx27h");
</script> -->


  <meta charset="utf-8" />
  <link rel="icon" id="favicon" href="%PUBLIC_URL%/images/logo-icon.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <!-- <meta name="theme-color" content="#000000" /> -->
  <meta name="description" content="Video KYC" />
  <meta name='og:image' content='%PUBLIC_URL%/images/Syntizen-logo.png'>
  
  <meta http-equiv="Content-Security-Policy" 
    content="
      worker-src blob:; 
      child-src blob: gap:;
      img-src 'self' blob: data:;
      default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: gap: content:">

  <link rel="apple-touch-icon" href="%PUBLIC_URL%/images/logo-icon.svg" />

  <!--clear cache-->
  <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  
  <!-- <link rel="manifest" href="%PUBLIC_URL%/manifest.json" /> -->

  <link rel="stylesheet" href="%PUBLIC_URL%/css/bootstrap.min.css" type="text/css">

  <!--css files-->
  <link rel="stylesheet" href="%PUBLIC_URL%/css/style.css" type="text/css">
  <link rel="stylesheet" href="%PUBLIC_URL%/css/loader.css" type="text/css">
  <link rel="stylesheet" href="%PUBLIC_URL%/css/miccheckmodal.css" type="text/css">
  <link rel="stylesheet" href="%PUBLIC_URL%/css/tide.css" type="text/css">
  <link rel="stylesheet" href="%PUBLIC_URL%/css/kinabank.css" type="text/css">
  <!-- font awsome link -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"
    type="text/css">
  <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"
    type="text/css"> -->

  

  <title>Video KYC</title>

  <style>
    body  {
      scroll-behavior: smooth !important;
    }
    .react-html5-camera-photo {
      position: static !important;
    }

    /* .react-html5-camera-photo>img, */
    .react-html5-camera-photo>video {
      width: 100% !important;
      border: 1.5px solid #ffffff;
      border-radius: 8px;
      object-fit: cover;
    }

    @media (max-width: 768px) {
      .react-html5-camera-photo>video {
        width: 100% !important;
        /* height: 180px; */
        height: 250px;
        object-fit: cover !important;
      }
    }

    @media (min-width: 991.98px) {
      .react-html5-camera-photo>video {
        width: 50% !important;
      }
    }

    #outer-circle {
      background-color: #fff !important;
      width: 65px !important;
      height: 65px !important;
    }

    #inner-circle {
      /* outline: 2px solid #000; */
      border: 2px solid #000;
      top: 50% !important;
    }

    .react-calendar {
      border: 0 !important;
    }
    input[type="date"],
    input::-webkit-date-and-time-value{
      /* color: red !important; */
      text-align: left !important;
    }
  </style>

</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <div id="portal-root"></div>

  <!-- <script src="%PUBLIC_URL%/js/jquery-3.2.1.min.js" type="text/javascript"></script> -->
  <!-- <script src="%PUBLIC_URL%/js/jquery-3.6.3.min.js" type="text/javascript"></script> -->
  <!-- <script src="%PUBLIC_URL%/js/jquery-3.7.1.min.js" type="text/javascript"></script> -->
  <script src="%PUBLIC_URL%/js/bootstrap.min.js" type="text/javascript"></script>
  <!-- <script type="text/javascript" src="public/js/newrelic.js"></script>   -->

  <canvas
  id="canvas"
></canvas>

  <script>

    document.addEventListener("keydown", function (event) {
      var key = event.key || event.keyCode;
      if (key == 123 || key == 16 || key == 17) {
        return false;
      } else if ((event.ctrlKey && event.shiftKey && key == 73)
        || (event.ctrlKey && event.shiftKey && key == 74)
        || (event.ctrlKey && event.shiftKey && key == 67)) {
        return false;
      }
    }, false);

  </script>

</body>

</html>
