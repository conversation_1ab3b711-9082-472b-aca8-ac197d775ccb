.idpage {
  background-color: #F7F9FA;
  min-width: 100vw;
  min-height: 100vh;
}
.idpage .debitardimg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.idpage .formmain {
  padding: 24px;
}
.idpage .formmain .forminput {
  display: flex;
  flex-direction: column;
}
.idpage .formmain .forminput label {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #666666;
  margin-top: 17px;
}
.idpage .formmain .forminput .customeridinput {
  background: #FFFFFF;
  border: 0.6px solid #D3D3D3;
  border-radius: 8px;
  height: 48px;
  margin-top: 5px;
  font-weight: 400;
  font-size: 18px;
  line-height: 25px;
  color: #333333;
  text-indent: 15px;
  letter-spacing: 1.5px;
  z-index: 1;
}
@media (max-width: 575.98px) {
  .idpage .customeridbtn {
    position: fixed;
    padding: 0 24px 24px 24px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}
.idpage .custidsubbtn {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38A1F7 0.1%, #38568F 99.85%);
  border-radius: 5px;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
  color: #FFFFFF;
  height: 45px;
  width: 100%;
  font-family: ProximaNova-semibold;
  outline: none;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.idpage .custidsubbtn:hover {
  color: #ffffff;
  transform: scale(1.05);
  box-shadow: 5px 10px 15px rgba(138, 138, 138, 0.562);
}
.idpage .customerdetailscard {
  background: #EBF5FF;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  padding-bottom: 20px;
}
.idpage .customerdetailscard .detailslable {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #999999;
}
.idpage .customerdetailscard .detailstext {
  margin-top: "7px";
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  color: #333333;
  word-break: break-all;
}/*# sourceMappingURL=kinabank.css.map */