// @font-face {
//     font-family: 'SF UI Display';
//     src: url('../fonts/SFUIDisplay-Regular.woff2') format('woff2'),
//         url('../fonts/SFUIDisplay-Regular.woff') format('woff');
//     font-weight: normal;
//     font-style: normal;
//     font-display: swap;
// }

@font-face {
    font-family: ProximaNova-Bold;
    src: url('../fonts/Proximanova/PROXIMANOVA-BOLD-WEBFONT.TTF');
}

@font-face {
    font-family: ProximaNova-semibold;
    src: url('../fonts/Proximanova/PROXIMANOVA-SBOLD-WEBFONT.TTF');
}

@font-face {
    font-family: ProximaNova-Regular;
    src: url('../fonts/Proximanova/PROXIMANOVA-REG-WEBFONT.TTF');
}

@font-face {
    font-family: ProximaNova-light;
    src: url('../fonts/Proximanova/PROXIMANOVA-LIGHT-WEBFONT.TTF');
}

@font-face {
    font-family: ProximaNova-thin;
    src: url('../fonts/Proximanova/PROXIMANOVA-THIN-WEBFONT.TTF');
}
