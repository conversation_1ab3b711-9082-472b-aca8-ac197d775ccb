@font-face {
    font-family: ProximaNova-Bold;
    src: url("../fonts/Proximanova/PROXIMANOVA-BOLD-WEBFONT.TTF");
  }
  @font-face {
    font-family: ProximaNova-semibold;
    src: url("../fonts/Proximanova/PROXIMANOVA-SBOLD-WEBFONT.TTF");
  }
  @font-face {
    font-family: ProximaNova-Regular;
    src: url("../fonts/Proximanova/PROXIMANOVA-REG-WEBFONT.TTF");
  }
  @font-face {
    font-family: ProximaNova-light;
    src: url("../fonts/Proximanova/PROXIMANOVA-LIGHT-WEBFONT.TTF");
  }
  @font-face {
    font-family: ProximaNova-thin;
    src: url("../fonts/Proximanova/PROXIMANOVA-THIN-WEBFONT.TTF");
  }
  @import 'mixin';
  @import 'fonts';
  @import 'colors';


.darkBG{
    position:fixed;
    background: rgba(0, 0, 0, 0.3);
    width: 100vw;
    height: 100vh;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1111;
}
.centered{
    position: fixed;
    /* left:40px;
    right: 40px; */
    background: #FFFFFF;
    border-radius: 5px;
    padding: 20px; 
}
@media (max-width: 575.98px) { 
    .centered{
        position: fixed;
        left:40px;
        right: 40px;
        background: #FFFFFF;
        border-radius: 5px;
        padding: 20px; 
        max-height: 95vh;
        overflow-y: auto;
    }
}

.imgcancel{
    float: right;
}
.mainheading{
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    text-align: center;
    color: #000000;
    margin-top:20px
}
.subheading{
    margin-top: 12px;
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    color: #000000;
}
.warning-text{
  color: red;
  text-align: justify;
  font-size: 13px;
  animation: dangerAlert 2s linear infinite;
}
.videopreview{
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #121212;
    opacity: 0.6;
    margin-top: -10px;
}
.videomain{
    margin-top: 7px;
    position: relative;
    width: 100%;
    height:300px;
    object-fit: cover;
    border-radius: 5px;
}
.videomain video{ 
    width: 100%;
    height:300px;
    object-fit: cover;
    border-radius: 5px;
}
.recordingsymbol{
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  align-items: center;
}
.video-preview-progress-status{
  font-size: 14px;
  text-align: center;
  margin-bottom: 20px;
}
.elipse1{    
    width: 18px;
    height: 18px;
    border: 1.5px solid #E80000;
    border-radius: 50%;
}
.elipse2{
    width: 11px;
    height: 11px;
    background-color: #E80000 ;
    border-radius: 50%;
    position: relative;
    left: 2.6px;
    top: 2.4px;
}
.videorunningtext{
    margin-left: 5px;
    margin-bottom: 0;
    font-weight: 600;
    font-size: 12px;
    line-height: 20px;
    color: #FFFFFF;
    opacity: 0.6;
    text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.26)
}

.recording-location-icon{
    position: absolute;
    top: 36px;
    left: 10.5px;
}
.recording-location-icon-tide{
  position: relative;
}
.voice-test-icon{
    position: absolute;
    bottom: 20px;
    left: 20px;
}
.mediaerror{
    position: absolute;
    top:100px;
    color: #E80000;
    text-align: center;

}
.pids-wrapper{
    display: flex;
    position:absolute;
    bottom: 10px;
    left: 34px;
}
.pid{
    width: 4vw;
    height: 4px;
    background-color:grey;
    margin-left: 4px;
}

/* for loading */
.loaderMain {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(229, 229, 229, 0.65);
    z-index: 9999;
  }
  
  .ldsring {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }
  .ldsring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    /* border: 8px solid rgb(1, 139, 126); */
    border-radius: 50%;
    -webkit-animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    /* border-color: rgb(1, 139, 126) transparent transparent transparent; */
  }
  .ldsring div:nth-child(1) {
    -webkit-animation-delay: -0.45s;
            animation-delay: -0.45s;
  }
  .ldsring div:nth-child(2) {
    -webkit-animation-delay: -0.3s;
            animation-delay: -0.3s;
  }
  .ldsring div:nth-child(3) {
    -webkit-animation-delay: -0.15s;
            animation-delay: -0.15s;
  }
  .ldsring p {
    text-align: center;
    margin: 0;
    margin-top: 10px;
    color:#000000;
    font-weight: 400;
    font-size: 14px;
  }
  
  @-webkit-keyframes lds-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes lds-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes dangerAlert {
    0%{
      color:red;
    }
    80%{
      color: red;
    }
    100%{
      color: white;
    }
  }
  @-webkit-keyframes dangerAlert {
    0%{
      color:red;
    }
    80%{
      color: red;
    }
    100%{
      color: white;
    }
  }
  /* .loading {
    display: inline-block;
    position: relative;
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
  }
  .loading div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 50px;
    height: 50px;
    margin: 8px;
    border: 5px solid white;
    border-radius: 50%;
    -webkit-animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: white transparent;
  }
  .loading div:nth-child(1) {
    -webkit-animation-delay: -0.45s;
            animation-delay: -0.45s;
  }
  .loading div:nth-child(2) {
    -webkit-animation-delay: -0.3s;
            animation-delay: -0.3s;
  }
  .loading div:nth-child(3) {
    -webkit-animation-delay: -0.15s;
            animation-delay: -0.15s;
  } */

  .ldsrings {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }
  .ldsrings div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 8px solid rgb(25, 41, 214);
    border-radius: 50%;
    -webkit-animation: lds-rings 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            animation: lds-rings 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: rgb(25, 41, 214) transparent transparent transparent;
  }
  .ldsrings div:nth-child(1) {
    -webkit-animation-delay: -0.45s;
            animation-delay: -0.45s;
  }
  .ldsrings div:nth-child(2) {
    -webkit-animation-delay: -0.3s;
            animation-delay: -0.3s;
  }
  .ldsrings div:nth-child(3) {
    -webkit-animation-delay: -0.15s;
            animation-delay: -0.15s;
  }
  .ldsrings p {
    text-align: center;
    margin: 0;
    margin-top: 10px;
    color:#000000;
    font-weight: 400;
    font-size: 14px;
  }
  
  @-webkit-keyframes lds-rings {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes lds-rings {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .ldsringcashbook {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }
  .ldsringcashbook div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 8px solid rgb(25, 41, 214);
    border-radius: 50%;
    -webkit-animation: ldsring-cashbook 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            animation: ldsring-cashbook 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: rgb(25, 41, 214) transparent transparent transparent;
  }
  .ldsringcashbook div:nth-child(1) {
    -webkit-animation-delay: -0.45s;
            animation-delay: -0.45s;
  }
  .ldsringcashbook div:nth-child(2) {
    -webkit-animation-delay: -0.3s;
            animation-delay: -0.3s;
  }
  .ldsringcashbook div:nth-child(3) {
    -webkit-animation-delay: -0.15s;
            animation-delay: -0.15s;
  }
  .ldsringcashbook p {
    text-align: center;
    margin: 0;
    margin-top: 10px;
    color:#000000;
    font-weight: 400;
    font-size: 14px;
  }
  
  @-webkit-keyframes ldsring-cashbook {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes ldsring-cashbook {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }


.modalbackground{
  position: absolute;
  background-color: rgba(0, 0, 0, 0.4);
  min-width: 100vw;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
}

.modalbody{
  position: absolute;
  box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.4);
  background: rgba(242, 242, 242, 0.8);
  backdrop-filter: blur(54.3656px);
  border-radius: 14px;
  z-index: 999999;
  /* width: 270px; */
  margin:10%;
}

.title{
  padding: 5%;
  font-weight: 600;
  font-size: 17px;
  line-height: 22px;
  text-align: center;
  letter-spacing: -0.408px;
  color: #000000;
}

.modalborder{
  border: 1px solid rgba(0, 0, 0, 0.2);
  width: 100%;
}

.allowoption{
  display: inline-block;
  align-items: center;
  justify-content: space-around;
  font-weight: 400;
  font-size: 18px;
  width: 100%;
}

.dontallow{
  display: inline-block;
  line-height: 22px;
  text-align: center;
  letter-spacing: -0.408px;
  padding: 10px;
  width: 50%;
  background-color: #d71a1a87;
  color: white;
  border-radius: 0 0 0 10px;
}

.allow{
  display: inline-block;
  line-height: 22px;
  text-align: center;
  letter-spacing: -0.408px;
  padding: 10px;
  width: 50%;
  background-color: #449944;
  color: white;
  border-radius: 0 0 10px 0;
}
