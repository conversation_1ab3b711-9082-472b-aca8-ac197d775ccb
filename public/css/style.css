/* By Uk */
@font-face {
  font-family: ProximaNova-Bold;
  src: url("../fonts/Proximanova/PROXIMANOVA-BOLD-WEBFONT.TTF");
}
@font-face {
  font-family: ProximaNova-semibold;
  src: url("../fonts/Proximanova/PROXIMANOVA-SBOLD-WEBFONT.TTF");
}
@font-face {
  font-family: ProximaNova-Regular;
  src: url("../fonts/Proximanova/PROXIMANOVA-REG-WEBFONT.TTF");
}
@font-face {
  font-family: ProximaNova-light;
  src: url("../fonts/Proximanova/PROXIMANOVA-LIGHT-WEBFONT.TTF");
}
@font-face {
  font-family: ProximaNova-thin;
  src: url("../fonts/Proximanova/PROXIMANOVA-THIN-WEBFONT.TTF");
}
html {
  scroll-behavior: smooth;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  font-family: ProximaNova-Regular;
  overscroll-behavior: contain;
}

body::-webkit-scrollbar,
.app-body::-webkit-scrollbar {
  width: 5px;
}

body::-webkit-scrollbar-track,
.app-body::-webkit-scrollbar-track {
  background-color: #dbdbdb;
}

body::-webkit-scrollbar-thumb,
.app-body::-webkit-scrollbar-thumb {
  background-color: #647faf;
  border-radius: 6px;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.panbtn {
  width: 100%;
  height: 45px;
  background-color: #ffffff;
  margin-top: 10px;
  color: #000000;
  border: 1.5px solid rgb(1, 139, 126);
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn {
  width: 100%;
  height: 45px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  color: #ffffff;
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.btn.btn-white {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, rgba(56, 161, 247, 0.151) 0.1%, rgba(56, 87, 143, 0.151) 99.85%);
  color: #38568f;
}
.btn.btn-white-no-opacity {
  background: rgb(179, 192, 203);
}
.btn:hover {
  color: #ffffff;
  transform: scale(1.05);
  box-shadow: 5px 10px 15px rgba(138, 138, 138, 0.562);
}
.btn:hover.btn-white {
  color: rgb(75, 109, 224);
}
.btn.retake {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #f73838 0.1%, #f05454 99.85%);
}
.btn:focus {
  box-shadow: none;
}

.okycbottom {
  margin-bottom: 200px;
}

.cancel-schedule-btn {
  width: auto;
  height: 30px;
  padding: 0px 8px;
  background-color: #ff002f;
  color: #ffffff;
  font-family: ProximaNova-semibold;
  font-size: 12px;
  outline: none;
  border-radius: 20px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.cancel-schedule-btn:hover {
  transform: scale(1.05);
  box-shadow: 5px 10px 15px rgba(138, 138, 138, 0.562);
}

.cancel-schedule-cashbook-btn {
  width: auto;
  height: 30px;
  padding: 0px 8px;
  background-color: #ff002f;
  color: #ffffff;
  font-family: ProximaNova-semibold;
  font-size: 12px;
  outline: none;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.cancel-schedule-cashbook-btn:hover {
  transform: scale(1.05);
  box-shadow: 5px 10px 15px rgba(138, 138, 138, 0.562);
}

.pandisplaytext {
  text-align: center;
  margin-top: 30px;
  color: rgb(0, 13, 46);
  font-size: 20px;
  font-weight: 600;
}

.close {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: -5px !important;
}
.close:focus {
  outline: none;
}

.close1 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 100% !important;
}
.close1:focus {
  outline: none;
}

.w-auto {
  width: auto !important;
}

.mb-20 {
  margin-bottom: 10px !important;
}

.mb-10p {
  margin-bottom: 30% !important;
}

input::-moz-placeholder {
  font-family: ProximaNova-Regular;
}

input::placeholder {
  font-family: ProximaNova-Regular;
}

input:focus,
select:focus {
  transition: all 300ms ease;
  outline-color: rgb(80, 166, 247);
}

.center, .fileicon-display, .app .app-body .app-body-steps li .step-nbr, .app .app-body .app-body-imgs, .app .app-body .app-body-img, .app .app-header, .main {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-absolute {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.zIndex {
  z-index: 99999;
}

.title {
  color: #16161e;
  margin-bottom: 6px;
  font-size: 20px;
  font-family: ProximaNova-semibold;
}

.txt {
  color: #7e7e7e;
  font-size: 18px;
  font-family: ProximaNova-Regular;
}

.inline {
  display: inline;
}

.txt-color, .secret-codes .scretet-inp {
  background: linear-gradient(90deg, #38568f 0%, #38a1f7 93.85%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.f-bold {
  font-family: ProximaNova-Bold;
}

.main {
  width: 100%;
  height: auto;
  background-color: #d3e1f7;
  position: relative;
}
.main .watermark {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  opacity: 0.06;
  z-index: 1;
}
.main .watermark img {
  -o-object-fit: cover;
     object-fit: cover;
}
.main .watermark .watermark-title {
  margin-top: 10px;
  font-weight: 600;
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .main {
    background-color: #fff;
  }
}
@media (max-width: 575.98px) {
  .main {
    background-color: #fff;
  }
}
.main #agent {
  z-index: 0;
}
.main #agent .vd-bx-agnt-resume-vd {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: rgba(37, 37, 37, 0.515);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  flex-direction: column;
}
.main #agent .vd-bx-agnt-resume-vd img {
  margin-bottom: 5px;
  width: 50px;
  height: 40px;
  animation: spinner 900ms linear infinite;
}
.main .remote {
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  height: -webkit-fill-available;
  position: absolute;
  top: 0;
  left: 0;
}
.main .local {
  width: 40%;
  height: 17%;
  position: fixed;
  right: 10px;
  top: 10px;
  z-index: 2;
  border: 1.5px solid white;
  border-radius: 12px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .main .local {
    width: 190px !important;
    height: 200px !important;
  }
}
@media (min-width: 992px) and (max-width: 1599.98px) {
  .main .local {
    width: 190px !important;
    height: 200px !important;
  }
}
@media (min-width: 1600px) and (max-width: 1919.98px) {
  .main .local {
    width: 190px !important;
    height: 200px !important;
  }
}
@media (min-width: 1920px) and (max-width: 2559.98px) {
  .main .local {
    width: 190px !important;
    height: 200px !important;
  }
}
@media (min-width: 2560px) and (max-width: 4096px) {
  .main .local {
    width: 190px !important;
    height: 200px !important;
  }
}
.main .cus {
  border: 10px solid green;
}
.main .agent {
  width: 100% !important;
  -o-object-fit: cover !important;
  object-fit: cover !important;
  height: 100% !important;
  /* object-fit: fill; */
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}
.main .agent video {
  height: 100%;
}

.app {
  position: relative;
  width: 70%;
  min-height: 100vh;
  background-color: hsla(0, 0%, 100%, 0.95);
  border-radius: 1px;
  overflow: hidden;
}
.app.large-device-app {
  width: 100% !important;
  background-color: transparent !important;
}
.app.large-device-app .instructions-list {
  list-style-type: decimal;
}
@media (max-width: 575.98px) {
  .app {
    width: 100%;
  }
}
.app .app-header {
  min-height: 55px;
  position: sticky;
  top: 0;
}
.app .app-header.app-header-dark {
  background-color: #000 !important;
}
.app .app-header .app-goback {
  position: absolute;
  left: 8%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  width: 34px;
  height: 34px;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  outline: none;
  transition: all 350ms ease;
  z-index: 9999;
}
.app .app-header .app-goback:hover {
  box-shadow: 4px 4px 15px rgba(85, 85, 85, 0.37);
}
.app .app-header .app-goback img {
  width: 14px;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  position: relative;
  top: -2px;
  transform: rotate(180deg);
}
.app .app-header .app-header-title {
  color: #000000;
  font-size: 20px;
  font-family: ProximaNova-semibold;
  margin: 20px;
  text-align: center;
}
.app .tc-merged-screen {
  padding: 2% 4% 6% 4%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: relative;
  z-index: 10;
  height: calc(100vh - 80px);
}
.app .app-body {
  padding: 2% 8% 6% 8%;
  height: calc(100vh - 55px);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: relative;
  z-index: 10;
}
@media (max-width: 575.98px) {
  .app .app-body {
    height: calc(100vh - 160px);
  }
}
.app .app-body .main-heading {
  color: #000000;
  text-align: center;
  font-family: ProximaNova-semibold;
  margin-block: 7%;
  font-size: 40px;
}
.app .app-body .app-start {
  flex: 1;
}
.app .app-body .app-start.l-app-start {
  display: flex;
  align-items: center;
  justify-content: center;
}
.app .app-body .app-start.l-app-start .bgWhite {
  background-color: hsla(0, 0%, 100%, 0.82);
  padding: 3%;
  border-radius: 6px;
}
.app .app-body .app-start .languages-list {
  list-style: none;
  padding-left: 0;
  margin: 0;
}
.app .app-body .app-start .languages-list .languages-list-item {
  color: #121212;
  padding: 12px 0px;
  font-family: ProximaNova-semibold;
  font-size: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  cursor: pointer;
  transition: all 400ms ease;
}
.app .app-body .app-start .languages-list .languages-list-item:hover {
  background-color: rgba(240, 240, 240, 0.445);
}
.app .app-body .app-start .languages-list .languages-list-item:nth-last-child(1) {
  border-bottom: 0px;
}
.app .app-body .app-start .languages-list .languages-list-item .languages-list-check {
  position: absolute;
  right: 0%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.app .app-body .app-end {
  padding: 4% 0%;
}
.app .app-body .app-adhaar-upload {
  display: flex;
  align-items: center;
  border: 1px solid #38a1f7;
  border-radius: 5px;
  position: relative;
  padding: 13px;
}
.app .app-body .app-adhaar-upload .upload-file {
  position: absolute;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  cursor: pointer;
  color: transparent;
}
.app .app-body .app-adhaar-upload .upload-file::-webkit-file-upload-button {
  visibility: hidden;
}
.app .app-body .app-adhaar-upload .app-adhaar-upload-content {
  flex: 1;
  margin-right: 16%;
}
.app .app-body .app-adhaar-upload .app-adhaar-upload-content .title2 {
  margin-bottom: 2px;
  font-size: 16px;
  font-family: ProximaNova-semibold;
  color: #000000;
}
.app .app-body .app-adhaar-upload .app-adhaar-upload-content .txt {
  margin: 0;
  color: #121212;
  font-size: 12px;
}
.app .app-body .banner {
  width: 100%;
  margin-bottom: 15%;
  text-align: center;
}
.app .app-body .banner img {
  width: 100%;
  height: auto;
}
.app .app-body .app-body-img {
  position: relative;
  text-align: center;
  width: 150px;
  height: 150px;
  margin: 0 auto;
  margin-bottom: 10%;
}
.app .app-body .app-body-img img {
  width: 80%;
  height: auto;
  -o-object-fit: contain;
     object-fit: contain;
}
.app .app-body .app-body-imgs {
  position: relative;
  text-align: center;
  width: 150px;
  height: 150px;
  margin: 0 auto;
}
.app .app-body .app-body-imgs img {
  width: 80%;
  height: auto;
  -o-object-fit: contain;
     object-fit: contain;
}
.app .app-body .app-body-data {
  text-align: center;
  padding: 3% 0%;
}
.app .app-body .app-body-steps {
  list-style: none;
  padding-left: 0;
  margin-bottom: 10%;
}
.app .app-body .app-body-steps li {
  margin-bottom: 45px;
  display: flex;
  align-items: center;
  position: relative;
}
.app .app-body .app-body-steps li:nth-last-child(1) {
  margin-bottom: 0;
}
.app .app-body .app-body-steps li:nth-last-child(1)::before {
  display: none;
}
.app .app-body .app-body-steps li::before {
  content: "";
  position: absolute;
  left: 14px;
  top: 60px;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 30px;
  background-color: #ececec;
}
.app .app-body .app-body-steps li .step-nbr {
  width: 28px;
  height: 28px;
  background-color: #ececec;
  margin-right: 6px;
  font-size: 12px;
  font-family: ProximaNova-Bold;
  border-radius: 50%;
}
.app .app-body .app-body-steps li .step-name {
  margin-bottom: 0;
  font-size: 14px;
}
.app .app-body .camera {
  width: 100%;
  height: auto;
  margin-bottom: 10%;
}
.app .app-body .camera #video {
  width: 100%;
  height: 100vh !important;
  height: auto;
  border: 2px solid grey;
}
.app .app-body .capture-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #7e7e7e;
  outline: 4px solid #7e7e7e;
  border: 2px solid #fff;
  cursor: pointer;
  margin: 5% 0%;
}
.app .app-body .output {
  width: 100%;
}
.app .app-body .output img {
  width: 100%;
  height: auto;
}
.app .app-body .app-body-select-doc {
  margin-bottom: 10%;
  margin-top: 10%;
}
.app .app-body .app-body-select-doc .select-doc-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #16161e;
  padding: 10px 0px;
  border-top: 1px solid #ececec;
  border-bottom: 1px solid #ececec;
  transition: all 350ms ease;
  position: relative;
  z-index: 1;
}
.app .app-body .app-body-select-doc .select-doc-link::before {
  content: "";
  position: absolute;
  top: 0%;
  left: 0%;
  width: 0%;
  height: 100%;
  background-color: #ececec;
  z-index: -1;
  transition: all 350ms ease;
}
.app .app-body .app-body-select-doc .select-doc-link:hover {
  text-decoration: none;
}
.app .app-body .app-body-select-doc .select-doc-link:hover::before {
  width: 100%;
}
.app .app-body .app-body-select-doc .select-doc-link figure {
  display: flex;
  align-items: center;
  margin: 0;
}
.app .app-body .app-body-select-doc .select-doc-link figure img {
  width: 30px;
  -o-object-fit: contain;
     object-fit: contain;
  height: auto;
  margin-right: 17px;
}
.app .app-body .app-body-select-doc .select-doc-link .btn-select-doc {
  bottom: 0;
  height: 30px;
  background-color: #fff;
  width: 34px;
  height: 34px;
  box-shadow: 4px 4px 15px rgba(85, 85, 85, 0.192);
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  outline: none;
}
.app .app-body .app-body-select-doc .select-doc-link .btn-select-doc img {
  width: 20px;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  position: relative;
  top: -2px;
}
.app .app-body .app-display-pic {
  width: 100%;
  height: 190px;
  border: 1px solid rgb(168, 168, 168);
  border-radius: 4px;
  text-align: center;
  margin-bottom: 20%;
  margin-top: 10%;
}
.app .app-body .app-display-pic.selfie-video {
  height: 340px;
}
.app .app-body .app-display-pic img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.app .app-body .display-pic-txt {
  margin-bottom: 20%;
}

.video-guide {
  padding-left: 10px;
  margin: 0;
  font-size: 15px;
  list-style: none;
}
.video-guide li {
  margin-bottom: 15px;
  display: flex;
}
.video-guide li .identity-names {
  font-family: ProximaNova-semibold;
  margin: 0;
  color: #121212;
  font-size: 18px;
}
.video-guide li .identity-names:hover {
  text-decoration: none;
}

.cus-consentBtn {
  width: 90%;
  margin: 0 auto;
  position: fixed;
  bottom: 6px;
  border-radius: 6px;
  left: 5%;
}

.dark {
  background-color: #16161e;
}
.dark .app-header {
  background-color: #16161e;
}
.dark .app-header .app-header-title {
  color: #fff;
}

.app-body-frm {
  padding: 3% 0%;
}

.frm-grp {
  position: relative;
  margin-bottom: 14px;
}
.frm-grp .frm-grp-inp {
  width: 100%;
  height: 45px;
  border: 1px solid rgb(168, 168, 168);
  color: #16161e;
  border-radius: 6px;
  padding: 0px 10px;
  font-family: ProximaNova-Bold;
}

.secret-codes {
  margin-block: 2%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.secret-codes .scretet-inp {
  width: 40px;
  border: 0;
  border-bottom: 3px solid #38568f;
  outline: none;
  padding: 2px;
  background-color: grey;
  margin: 10px;
  font-family: ProximaNova-Bold;
  font-size: 26px;
  text-align: center;
}

.secret-code {
  justify-content: center;
  gap: 1rem;
}

.otp-txt {
  color: rgb(75, 109, 224);
  display: block;
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
}
.otp-txt:hover {
  text-decoration: none;
}

.custom-control-input:checked ~ .custom-control-label::before {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
}

@media (max-width: 575.98px) {
  .custom-dialog-end {
    align-items: flex-end !important;
    margin: 0;
    min-height: 100% !important;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .custom-dialog-end {
    align-items: flex-end !important;
    margin: 0;
    min-height: 100% !important;
  }
}
@media (max-width: 575.98px) {
  .custom-dialog-end.modal-dialog {
    max-width: 100% !important;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .custom-dialog-end.modal-dialog {
    max-width: 100% !important;
  }
}
.custom-dialog-end .modal-content {
  border-radius: 25px 25px 0px 0px;
}

.spinner {
  position: fixed;
  text-align: center;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(54, 54, 54, 0.2588235294);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
}

.spinner > div {
  width: 25px;
  height: 25px;
  background-color: #38a1f7;
  border-radius: 100%;
  display: inline-block;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  animation-delay: -0.16s;
}
@keyframes sk-bouncedelay {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
.aadhar-details .cus-textareaKYC textarea {
  height: 100%;
  padding-top: 2rem;
  border-top: solid #e9ecef 1.5rem !important;
  border-radius: 0;
}
.aadhar-details .cus-textareaKYC textarea label {
  z-index: 1;
}

.ocr-pht {
  width: 100px;
  height: 100px;
  border: 3px solid rgba(13, 202, 240, 0.431372549);
  border-radius: 3px;
  margin-bottom: 10px;
}
.ocr-pht img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.cus-ocrInput {
  position: relative;
}
.cus-ocrInput .cus-PencilIcon {
  position: absolute;
  font-size: 10px;
  top: 50%;
  right: 5%;
  opacity: 0.5;
}

.errorTxt {
  position: absolute;
  color: #ff002f;
  margin-bottom: 1rem;
  font-size: 0.8rem;
}

.filename-display {
  position: absolute;
  top: 25%;
  opacity: 1;
  left: 4px;
  font-size: 15px;
  display: inline-block;
  width: 80%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}

.fileicon-display {
  position: absolute;
  font-size: 10px;
  top: 50%;
  opacity: 1;
  transform: translate(-50%, -50%);
  right: -15px;
  border-radius: 5px;
  padding: 3px;
  width: 32px;
  height: 32px;
  z-index: -1;
}
.fileicon-display img {
  width: 17px;
}

.spin-loader {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.display-qtn {
  position: absolute;
  text-align: center;
  width: 100%;
  bottom: 16%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 99999;
}
.display-qtn span {
  width: 90%;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
}

.upload-btn-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
}
.upload-btn-wrapper .Pan_Cap_heading {
  font-size: 20px;
}
.upload-btn-wrapper .cus-pan_proceed {
  color: #ffffff;
}

.upload-btn-pan {
  border: 2px solid gray;
  color: gray;
  background-color: white;
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 20px;
  font-weight: bold;
}

.upload-btn-wrapper input[type=file] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

.upload-displayimg {
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  bottom: 0px;
}

.display-btn {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 40px;
  width: 100%;
  padding: 20px;
}

.capture-photo {
  font-size: 18px;
  font-weight: 400;
  color: #ffffff;
}

.text-wrapping {
  border: 1px solid;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  padding: 4px;
  width: 120px;
  display: inline-block;
  border-radius: 6px;
}

.tidepanbtn {
  background: transparent;
  border-radius: 256px;
  height: 44px;
  font-weight: bold;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.5px;
  border: none;
  margin-top: 10px !important;
  color: #1929d6 !important;
}

.panimg {
  border-radius: 10px;
  width: auto;
  height: 250px;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: center;
     object-position: center;
  border: 0.712766px solid #D0D0D0;
}

.panimg1 {
  padding: 30px;
  border: 1.5px solid rgb(1, 139, 126);
  height: 300px;
  border-radius: 10px;
  width: auto;
  height: 250px;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: center;
     object-position: center;
  border: 0.712766px solid #d0d0d0;
}

@media (min-width: 991.98px) {
  .panimg {
    width: 50%;
  }
}
.video-play {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  height: -webkit-fill-available;
  /* object-fit: fill; */
  position: absolute;
  top: 0;
  left: 0;
}

.warning-msg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
}
.warning-msg .warning-msg-bx {
  width: 240px;
  height: 300px;
  border-radius: 14px;
  background: rgba(242, 242, 242, 0.8);
  display: flex;
  align-content: space-between;
  flex-direction: column;
}
@media (max-width: 575.98px) {
  .warning-msg .warning-msg-bx {
    width: 90%;
  }
}
.warning-msg .warning-msg-bx-top {
  flex: 1;
  padding: 4%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.warning-msg .warning-msg-bx-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 14px;
}
.warning-msg .warning-msg-btn {
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.timer {
  position: absolute;
  top: 13px;
  left: 12px;
  padding: 2px 14px 2px 15px;
  background-color: rgb(242, 78, 30);
  color: #ffffff;
  z-index: 2;
  border-radius: 10px;
}

.ovel-canvas {
  position: absolute;
  top: 0%;
  left: 0%;
  -o-object-fit: cover;
  object-fit: cover;
  background-size: cover;
  height: 100vh;
  width: 100%;
}
.ovel-canvas .ovel-canvas-img {
  position: absolute;
  top: 0%;
  left: 0%;
  -o-object-fit: cover;
  object-fit: cover;
  background-size: cover;
  height: 100vh;
  width: 100%;
  z-index: 1;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ovel-canvas .ovel-canvas-img {
    -o-object-fit: initial;
    object-fit: initial;
  }
}
@media (min-width: 992px) and (max-width: 1599.98px) {
  .ovel-canvas .ovel-canvas-img {
    -o-object-fit: initial;
    object-fit: initial;
  }
}
@media (min-width: 1600px) and (max-width: 1919.98px) {
  .ovel-canvas .ovel-canvas-img {
    -o-object-fit: initial;
    object-fit: initial;
  }
}
@media (min-width: 1920px) and (max-width: 2559.98px) {
  .ovel-canvas .ovel-canvas-img {
    -o-object-fit: initial;
    object-fit: initial;
  }
}
@media (min-width: 2560px) and (max-width: 4096px) {
  .ovel-canvas .ovel-canvas-img {
    -o-object-fit: initial;
    object-fit: initial;
  }
}

.cus-PencilIcon {
  position: absolute;
  font-size: 10px;
  top: 50%;
  right: 5%;
  opacity: 0.5;
}

.display-img {
  position: relative;
  z-index: 9;
  padding: 20px 20px 0 20px;
}

.pan_bg {
  background-color: black;
  padding: 20px;
  position: relative;
  z-index: 9;
}
@media (max-width: 575.98px) {
  .pan_bg {
    height: calc(100vh - 100px);
  }
}
.pan_bg .video_pan {
  margin: 50px 0;
  width: 100%;
  height: 220px;
  background: #323131;
  border: 1.5px solid #ffffff;
  border-radius: 8px;
}
.pan_bg .video_pan .video {
  width: 100%;
  height: 250px !important;
  border-radius: 8px;
  -o-object-fit: cover;
     object-fit: cover;
}
.pan_bg .video_pan .canvas {
  display: none;
}
.pan_bg .VerificationTextStyled {
  color: #fff;
  margin-top: 20px;
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
  margin-bottom: 0px !important;
}
.pan_bg .VerificationSmallTextStyled {
  margin-top: 4px !important;
  font-style: normal;
  font-size: 18px;
  text-align: center;
  color: #e9e5e5;
  opacity: 0.6;
  padding: 0 30px;
}

.camera-flex {
  display: flex;
  justify-content: center;
}
.camera-flex .camera-div {
  position: absolute;
  bottom: 0px;
  width: 90%;
  color: white;
}
.camera-flex .camera-div .camera-btn {
  display: flex;
  justify-content: center;
}
.camera-flex .camera-div .camera-btn .camera-svg:active {
  padding: 0px 0px 1px 0px;
}
.camera-flex .camera-div .camera-content {
  display: flex;
  justify-content: space-between;
  margin: 20px 0px;
}

.cstmr-brwsr-dtls-mn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  animation: down 400ms linear;
  position: relative;
  z-index: 999;
  background: #e5e5e5;
}

.cstmr-brwsr-dtls {
  position: relative;
  padding: 6% 3%;
  width: 85%;
  background: #ffffff;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  z-index: 99;
}
.cstmr-brwsr-dtls .browserbg-img {
  position: absolute;
  left: 10%;
  top: 10%;
  z-index: -1;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-ttl {
  color: #000000;
  text-align: center;
  margin-bottom: 10px;
  margin-top: 20px;
  font-size: 20px;
  line-height: 1.5;
}
.cstmr-brwsr-dtls .cstmr-brwser-dtls-copytxt {
  color: #3799ff;
  display: inline-block;
  padding-right: 10px;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
  font-weight: bold;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-sbttl {
  color: rgb(78, 78, 78);
  margin-bottom: 10px;
  text-align: center;
  font-size: 15px;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-bx {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  min-height: 162px;
  background: #ffffff;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-bx .cstmr-brwsr-dtls-bx-img {
  width: 40px;
  height: 40px;
  border-radius: 2px;
  margin-bottom: 10px;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-bx .cstmr-brwsr-dtls-bx-img img {
  -o-object-fit: cover;
     object-fit: cover;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-bx .cstmr-brwsr-dtls-bx-cntnt {
  flex: 1;
  text-align: center;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-bx .cstmr-brwsr-dtls-bx-cntnt .cstmr-brwsr-dtls-bx-ttl {
  margin-bottom: 4px;
  font-size: 16px;
  font-weight: bold;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-bx .cstmr-brwsr-dtls-bx-cntnt .cstmr-brwsr-dtls-bx-txt {
  color: rgb(40, 39, 39);
  margin-bottom: 3px;
  font-size: 14px;
}
.cstmr-brwsr-dtls .cstmr-brwsr-dtls-bx .cstmr-brwsr-dtls-bx-cntnt .cstmr-brwsr-dtls-bx-txt span {
  color: #018b7f;
  font-weight: bold;
}

@keyframes down {
  0% {
    transform: translateY(50px);
    opacity: 0.5;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.themeBtn {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  color: #ffffff;
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 2px 12px;
}

.themeBtn:active {
  transform: scale(0.98);
  /* Scaling button to 0.98 to its original size */
  box-shadow: 3px 2px 22px 1px rgba(0, 0, 0, 0.24);
  /* Lowering the shadow */
}

.modalAudioVideoCheck {
  background: #ffffff;
  border-radius: 5px;
  padding: 34px 18px;
}

.heading {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
  color: #000000;
}

.para {
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 17px;
  color: #000000;
}

.videoPreview {
  font-style: normal;
  font-weight: 400;
  font-size: 17px;
  line-height: 20px;
  color: #121212;
  opacity: 0.6;
}

.checkVideo {
  box-sizing: border-box;
  position: absolute;
  background: #333333;
  border: 0.6px solid #d0d0d0;
  border-radius: 5px;
  background-image: url("images/checkAudiovideo.png");
}

.audioCheck {
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 20px;
  color: #121212;
  opacity: 0.6;
}

.ppBackground {
  position: absolute;
  width: 22px;
  height: 22px;
  background: #d9d9d9;
  border-radius: 5px;
}

#container-circles {
  bottom: 125px !important;
  position: fixed !important;
  background-color: black !important;
}

.chat {
  width: 100%;
  height: auto;
  position: relative;
}
.chat .chat-box {
  width: 100%;
  min-height: 200px;
  height: 70vw;
  max-height: 370px;
  background-color: #ffffff;
  overflow-y: scroll;
  padding: 0.8rem;
}
.chat .chat-box::-webkit-scrollbar {
  width: 0px;
}
.chat .chat-box::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}
.chat .chat-box::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.137);
  border-radius: 6px;
}
.chat .chat-box .message-box {
  display: flex;
  margin-bottom: 3%;
  font-family: ProximaNova-Regular;
}
.chat .chat-box .message-box p:first-child {
  margin: 0px;
  padding: 2px;
  word-break: break-all;
}
.chat .chat-box .message-box p:nth-child(2) {
  color: #666666;
  font-weight: 400;
  font-size: 9px;
  line-height: 24px;
  margin-bottom: -8px !important;
  position: relative;
  float: right;
}
.chat .chat-box .message-box.message-box1 {
  justify-content: flex-end;
  color: white;
}
.chat .chat-box .message-box.message-box2 {
  justify-content: flex-start;
  color: black;
}
.chat .chat-box .message-box .bank-msg {
  max-width: 80%;
}
.chat .chat-box .message-box .user-msg {
  max-width: 80%;
}
.chat .chat-box .message-box .media {
  align-items: flex-end;
}
.chat .chat-box .message-box .media-body {
  border-radius: 10px;
  padding: 5px 10px;
}
.chat .chat-box .message-box .media-body.chat-left {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #333333;
  background: #e7f4ff;
  border-radius: 0px 8px 8px 8px;
}
.chat .chat-box .message-box .media-body.chat-right {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  border-radius: 8px 0px 8px 8px;
}
.chat .chat-box .message-box .media-body .chat-msg {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
}
.chat .chat-box .message-box .media-body .chat-right .chat-msg {
  color: #ffffff;
}
.chat .chat-box .message-box .media-body .chat-time {
  font-weight: 400;
  font-size: 9px;
  line-height: 24px;
  color: #666666;
}
.chat .chat-form {
  font-family: ProximaNova-Regular;
  width: 100%;
  padding-bottom: 4px;
}
.chat .chat-form .chat-inp {
  width: 97%;
  display: block;
  background-color: white;
  height: 3rem;
  outline: none;
  padding: 0.5rem;
  color: black;
  border: 0.6px solid #c1c1c1;
  border-radius: 8px;
  margin-left: 5px;
  margin-top: -1px;
  padding-right: 2.2rem;
}
.chat .chat-form .chat-icon {
  position: absolute;
  top: 11px;
  color: black;
  background-color: transparent;
  border: 0px;
  padding: 0;
  cursor: pointer;
  outline: none;
}
.chat .chat-form .chat-icon.icon1 {
  left: 10px;
}
.chat .chat-form .chat-icon.icon2 {
  right: 1rem;
  top: 0.875rem;
}

p.chat-time.text-left {
  float: left !important;
}

.bot {
  position: fixed;
  bottom: 5%;
  right: 4%;
  z-index: 998;
}
.bot .chat-bot {
  position: fixed;
  bottom: 3%;
  right: 4%;
  width: 8vh;
  height: 8vh;
  border-radius: 200px;
  border: 0px;
  cursor: pointer;
  background: linear-gradient(180deg, #38568f 0%, #38a1f7 100%) !important;
  z-index: 9999;
  outline: none;
  color: #ffffff;
}
.bot .chat-bot.botactive {
  background-color: #1a488d;
}
.bot .bot-box {
  width: 90vw;
  max-width: 360px;
  border-radius: 8px;
  z-index: 9999;
  display: none;
}
.bot .bot-box.chatshow {
  display: block;
}
.bot .bot-box .bot-header {
  width: 100%;
  height: auto;
  padding: 10px;
  background-color: #fff;
  display: block;
}
.bot .bot-box .info-title {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  padding: 14px;
  color: #ffffff;
  border-radius: 8px 8px 0px 0px;
  margin-bottom: 0;
  height: 3rem;
  font-size: 16px;
  letter-spacing: 1px;
  align-items: center;
}
.bot .bot-box .infoicon {
  position: relative;
  float: right;
}

.close-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 400ms cubic-bezier(0.6, -0.28, 0.735, 0.045);
  animation: chatclose 0.9s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.chatcmp {
  position: relative;
  background: red;
  border-radius: 8px;
}

.inpCmp {
  background-color: #fff;
  border-radius: 0 0 0.5rem 0.5rem;
}

.notify-msg {
  width: 8vh;
  height: 8vh;
  background: #20a47e;
  position: absolute;
  right: 5%;
  top: 4%;
  z-index: -1;
  border-radius: 100px;
  display: none;
  animation: notify 1s linear infinite alternate;
}
.notify-msg.notifyactive {
  display: block;
}

@keyframes show {
  0% {
    transform: scale(0);
    transform-origin: 100% 100%;
    opacity: 0;
  }
  100% {
    transform: scale(1);
    transform-origin: 100% 100%;
    opacity: 1;
  }
}
@keyframes showin {
  0% {
    transform: scaleY(1);
    transform-origin: 0% 100%;
    opacity: 1;
  }
  100% {
    transform: scaleY(0);
    transform-origin: 0% 100%;
    opacity: 1;
  }
}
@keyframes chatclose {
  0% {
    transform: scaleY(0, 0);
    opacity: 0;
  }
  100% {
    transform: scaleY(1.1, 1.1);
    opacity: 1;
  }
}
@keyframes notify {
  0% {
    opacity: 0;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.2);
  }
}
.videobtn {
  margin-top: 10px;
  width: 100%;
  height: 36px;
  background: rgb(1, 139, 126);
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tidevideobtn {
  width: 100%;
  height: 36px;
  background: rgb(1, 139, 126);
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.locationtideenablemsg {
  font-weight: 500;
  font-size: 12px;
  color: #2f3037;
  letter-spacing: 0.4px;
  margin-left: 8px;
  margin-top: -20px;
}

.warning-popup {
  position: absolute;
  min-width: 40%;
  height: auto;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 4px 4px solid rgba(222, 222, 222, 0.097);
  background-color: rgba(22, 22, 30, 0.631372549);
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  z-index: 9999;
}

.ipdata {
  padding: 3px;
}

.tableContainer {
  width: 90%;
  margin: 0 auto;
}

.changeNtwkImg {
  width: 100%;
  text-align: center;
  margin-bottom: 20px;
}

.changeNtwkTxt {
  width: 100%;
  font-weight: 600;
  font-size: 20px;
  letter-spacing: 0.5px;
  margin-bottom: 20px;
  color: #ff815c;
  opacity: 0.8;
  text-align: center;
}

.connectNtwk {
  text-align: center;
  color: #121212;
  opacity: 0.6;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 20px;
}

.ioserr {
  text-align: center;
  color: red;
  opacity: 0.6;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 20px;
}

.closebutton {
  width: 100%;
  padding: 5px;
  text-align: end;
}

.keys {
  width: 23px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #333333;
  border-radius: 8px 0 0 0;
}

.values {
  width: 23px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #333333;
  border-radius: 0 8px 0 0;
}

.fontBold {
  font-weight: bold !important;
  margin-top: 9%;
}

.inspectBtn {
  height: 45px;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms;
}
.inspectBtn .trans-img {
  padding: 0px;
  text-align: center;
  filter: invert(1);
  width: 24px;
  padding-left: 12px;
}

.table > :not(:first-child) {
  border-top: none;
}

.pan_bg_tide {
  position: relative;
  height: 100vh;
  background-color: black;
  overflow-y: scroll;
}
.pan_bg_tide .panbgarrowicon {
  position: absolute;
  top: 40px;
  left: 20px;
  border: none;
  background-color: transparent;
}
.pan_bg_tide .react-html5-camera-photo > video {
  width: 90% !important;
  border: 1.5px solid #ffffff;
  border-radius: 16px;
  height: 250px !important;
  margin-top: 100px;
}
.pan_bg_tide .camerapositioninfo {
  display: flex;
  flex-direction: column;
  padding: 20px;
  margin-bottom: 120px;
}
.pan_bg_tide .camerapositioninfo .camerapositioninfomain {
  display: flex;
  margin-top: 10px;
}
.pan_bg_tide .camerapositioninfo .camerapositioninfomain img {
  margin-top: 1px;
  margin-right: 10px;
  width: 20px;
  height: 20px;
}
.pan_bg_tide .camerapositioninfo .camerapositioninfomain .camerapositioninfotext {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
  color: #ffffff;
  word-wrap: wrap;
}

.tidevideocalllangheading {
  margin-top: 60px;
  font-weight: 500;
  color: #0c0c0e;
  font-size: 24px;
  line-height: 32px;
}

.tidevideocalllanginfo {
  margin-top: 10px;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  margin-bottom: 20px;
}

.tidelangopt {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  transition: 0.3s;
  padding: 16px;
  margin-bottom: 2px;
  background: #ffffff;
}
.tidelangopt:nth-child(1) {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.tidelangopt:nth-last-child(1) {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.tidelangarrow {
  width: 17px;
  height: 13px;
  float: right;
  margin-top: 5px;
}

.tideloactionperheader {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #343434;
  margin-top: 100px;
}

.tideloactionperheaderinfo {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
  color: #5f606d;
  margin-top: 16px;
}

.tidelocationimg {
  margin-top: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tidecalope {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.kycstatusheader {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #0c0c0e;
}

.kycstausinfo {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  margin-top: 10px;
}

.kycstausimg {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
}

.kycsubmitstaus {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.tideheadermain {
  padding: 10px 0 0 30px;
}

.tideheaderbutton {
  margin-top: 15px;
  border: none;
  background-color: transparent;
  /* width: 21px;
    height: 15px; */
  margin-bottom: 10px;
}

.tidearrowicon {
  width: 21px;
  height: 15px;
}

.tideheadertext {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #343434;
}

.tidetokennumbertime {
  font-weight: 400;
  font-size: 34px;
  line-height: 40px;
  letter-spacing: 0.25px;
  color: #1929d6;
}

.tidetokennumbertitle {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #0c0c0e;
}

.bookaslotinfo {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  display: flex;
  align-items: flex-end;
  letter-spacing: 0.25px;
  color: #0c0c0e;
}

.tidetokennumber {
  font-weight: 400;
  font-size: 34px;
  line-height: 40px;
  letter-spacing: 0.25px;
  color: #1929d6;
}

.tidewaitingtxt {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  margin-top: 8px;
  margin-bottom: 8px;
}

.tidewaitinginfo {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
}

.tidewaitinginfos {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  margin-bottom: 50px;
}

.reschedulecalendertitle {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
}

.tidecalender {
  box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  margin-top: 40px;
}

.react-calendar {
  border-radius: 8px !important;
}

.selecttimelableforbookslot {
  margin-top: 32px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.1px;
  color: #0c0c0e;
}

.selecttimeforbookslot {
  width: 100%;
  margin-top: 10px;
  gap: 8px;
  box-shadow: 0 0px 8px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #e4e4e7;
  padding: 16px 12px 16px 16px;
  background-position: right 0.75rem center;
  margin-bottom: 100px;
}

.tideadhardetails {
  padding: 20px;
}

@media (max-width: 575.98px) {
  .bookslotbuttonposition {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}
@media (max-width: 600px) {
  .panverifybutton {
    position: fixed;
    bottom: 2%;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px;
    left: 50%;
    transform: translate(-50%, -2%);
    z-index: 9999;
  }
}
@media (min-width: 600px) and (max-width: 1200px) {
  .panverifybutton {
    position: fixed;
    bottom: 2%;
    display: flex;
    flex-direction: column;
    width: 70%;
    padding: 20px;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }
}
.selectlangbtn {
  position: fixed;
  bottom: 3.5%;
  padding: 0 30px 0 30px;
  width: 100%;
  left: 50%;
  padding-bottom: 4%;
  transform: translate(-50%, 0%);
  z-index: 9999;
}

@media (max-width: 575.98px) {
  .homepagebutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 3.5%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
    z-index: 9999;
  }
  .selectkycbutton {
    position: fixed;
    padding: 0 30px 100px 30px;
    bottom: 2%;
    width: 100%;
    left: 50%;
    height: 50px;
    transform: translate(-50%, 30%);
    z-index: 9999;
  }
}
@media (max-width: 575.98px) {
  .panbutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}
@media (max-width: 575.98px) {
  .joinnowbtntide {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}
@media (max-width: 575.98px) {
  .kyccombtn {
    position: fixed;
    padding: 10px 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
    z-index: 9999;
  }
}
.agentunavailableimg {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 78px;
}

.agentunavailabletxt {
  margin-top: 34px;
  font-weight: 600;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
  color: #000000;
}

.agentunavailableinfo {
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  text-align: center;
  color: #6e6e6f;
  margin-top: 16px;
}

@media (max-width: 575.98px) {
  .userconsentbutton {
    position: fixed;
    left: 50%;
    transform: translate(-50%, 2%);
    width: 90%;
    background-color: rgba(255, 255, 255, 0.9490196078);
    overflow: hidden;
    bottom: 0;
    height: 240px;
  }
}
@media (max-width: 575.98px) {
  .kycbutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
@media (max-width: 575.98px) {
  .pankycbutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
    z-index: 9999;
  }
}
@media (max-width: 575.98px) {
  .tidecameraaccessbutton {
    position: fixed;
    bottom: 0%;
    padding: 0 30px 0 30px;
    width: 100%;
    left: 50%;
    padding-bottom: 2%;
    transform: translate(-50%, -50%);
  }
}
.tidewaitinginfotxt {
  display: flex;
  margin-top: 16px;
}

.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  font-weight: bold;
  font-size: 20px;
  flex-direction: column;
  padding: 30px;
}
.nodata .text1 {
  font-size: 30px;
}
.nodata .link {
  text-align: center;
  word-break: break-all;
  display: block;
}

.userconsent {
  width: 100%;
  min-height: 100vh;
}

.watermarks {
  position: absolute;
  bottom: 0%;
  opacity: 0.3;
  z-index: 9999999;
  font-size: 17px;
  left: 7%;
}

.slidecontainer {
  width: 100%;
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 5px;
  border-radius: 5px;
  background: #d3d3d3;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: rgb(1, 139, 126);
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: rgb(1, 139, 126);
  cursor: pointer;
}

.cropPageSubHead {
  font-size: 16px;
  font-weight: 400;
}

.PanCardImageText {
  font-weight: 400;
  font-size: 14px;
}

.SliderHederTextCropPage {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  color: #121212;
  opacity: 0.6;
}
.SliderHederTextCropPage div {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.OccupationSelectionForm {
  line-height: 32px;
  font-size: 18px;
  font-weight: 400;
}

input[type=radio] {
  /* remove standard background appearance */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: auto;
  /* create custom radiobutton appearance */
  width: 17px;
  height: 17px;
  /* background-color only for content */
  background-clip: content-box;
  border: 2px solid gray;
  padding: 2px;
  border-radius: 50%;
  margin-right: 10px;
}

.customerreconnect {
  display: flex;
  justify-content: center;
  align-items: "center";
  position: absolute;
  top: 40%;
}

.custom-select {
  position: relative;
  display: inline-block;
  min-width: 8rem;
}

.selected-option {
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  min-width: 8rem;
  background: #E3EBF3;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.options-list {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1;
  list-style-type: none;
  margin: 0;
  padding: 0;
  width: 100%;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top: none;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  font-size: 16px;
  max-height: 180px;
  overflow: scroll;
}
.options-list li {
  padding: 10px;
  cursor: pointer;
  font-weight: 550;
  text-align: center;
  border-bottom: 1px solid rgb(254, 246, 246);
}
.options-list li:hover {
  color: red !important;
}

#canvas {
  display: none;
}/*# sourceMappingURL=style.css.map */