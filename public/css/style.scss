/* By Uk */
@import "mixin";
@import "fonts";
@import "colors";

$bgc: #d3e1f7;

html {
  scroll-behavior: smooth;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  font-family: ProximaNova-Regular;
  overscroll-behavior: contain;
}

body::-webkit-scrollbar,
.app-body::-webkit-scrollbar {
  width: 5px;
  // background-color: #16161E;
}

body::-webkit-scrollbar-track,
.app-body::-webkit-scrollbar-track {
  background-color: #dbdbdb;
}

body::-webkit-scrollbar-thumb,
.app-body::-webkit-scrollbar-thumb {
  background-color: #647faf;
  border-radius: 6px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.panbtn {
  width: 100%;
  height: 45px;
  // background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(269.82deg, #38A1F7 0.1%, #38568F 99.85%);
  // color: #ffffff
  background-color: #ffffff;
  margin-top: 10px;
  color: #000000;
  border: 1.5px solid rgb(1, 139, 126);
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn {
  width: 100%;
  height: 45px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
    linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  color: #ffffff;
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);

  // &.btn1{
  //     width: 230px;
  // }
  &.btn-white {
    // width: 230px;
    // background-color: #fff;
   
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
      linear-gradient(
        269.82deg,
        rgba(56, 161, 247, 0.151) 0.1%,
        rgba(56, 87, 143, 0.151) 99.85%
      );
    // opacity: 0.1;
    color: #38568f;
  }
  &.btn-white-no-opacity {
    background: rgba(179, 192, 203, 1);
  }

  &:hover {
    color: #ffffff;
    transform: scale(1.05);
    box-shadow: 5px 10px 15px rgba(138, 138, 138, 0.562);

    &.btn-white {
      color: rgb(75, 109, 224);
    }
  }

  &.retake {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
      linear-gradient(269.82deg, #f73838 0.1%, #f05454 99.85%);
  }

  &:focus {
    box-shadow: none;
  }
}

.okycbottom {
  margin-bottom: 200px;
}

.cancel-schedule-btn {
  width: auto;
  height: 30px;
  padding: 0px 8px;
  background-color: #ff002f;
  color: #ffffff;
  font-family: ProximaNova-semibold;
  font-size: 12px;
  outline: none;
  border-radius: 20px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);

  &:hover {
    transform: scale(1.05);
    box-shadow: 5px 10px 15px rgba(138, 138, 138, 0.562);
  }
}

.cancel-schedule-cashbook-btn {
  width: auto;
  height: 30px;
  padding: 0px 8px;
  background-color: #ff002f;
  color: #ffffff;
  font-family: ProximaNova-semibold;
  font-size: 12px;
  outline: none;
  // border-radius: 20px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);

  &:hover {
    transform: scale(1.05);
    box-shadow: 5px 10px 15px rgba(138, 138, 138, 0.562);
  }
}

.pandisplaytext {
  text-align: center;

  margin-top: 30px;
  // margin-bottom: 20px;
  color: rgb(0, 13, 46);
  font-size: 20px;
  font-weight: 600;
  // min-height: 55px;
}

.close {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  // border: 1px solid #647faf !important;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: -5px !important;

  &:focus {
    outline: none;
  }
}

.close1 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  // border: 1px solid #647faf !important;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 100% !important;

  &:focus {
    outline: none;
  }
}

.w-auto {
  width: auto !important;
}

.mb-20 {
  margin-bottom: 10px !important;
}

.mb-10p {
  margin-bottom: 30% !important;
}

input::placeholder {
  font-family: ProximaNova-Regular;
}

input:focus,
select:focus {
  transition: all 300ms ease;
  outline-color: rgb(80, 166, 247);
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-absolute {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.zIndex {
  z-index: 99999;
}

.title {
  color: #16161e;
  margin-bottom: 6px;
  font-size: 20px;
  font-family: ProximaNova-semibold;
}

.txt {
  color: #7e7e7e;
  // font-size: 14px;
  font-size: 18px;
  font-family: ProximaNova-Regular;
}
.inline{
  display: inline;
}

.txt-color {
  background: linear-gradient(90deg, #38568f 0%, #38a1f7 93.85%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.f-bold {
  font-family: ProximaNova-Bold;
}

.main {
  width: 100%;
  // min-height: 100vh;
  height: auto;
  background-color: $bgc;
  position: relative;

  .watermark {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    opacity: 0.06;
    z-index: 1;

    img {
      object-fit: cover;
    }

    .watermark-title {
      margin-top: 10px;
      font-weight: 600;
    }
  }

  @extend .center;

  @include sm {
    background-color: #fff;
  }

  @include xsm {
    background-color: #fff;
  }

    #agent {
        z-index: 0;
        
      .vd-bx-agnt-resume-vd {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        background-color: rgba(37, 37, 37, 0.515);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 99;
        flex-direction: column;

        img {
          margin-bottom: 5px;
          width: 50px;
          height: 40px;
          animation: spinner 900ms linear infinite;
        }
      }
    }

  .remote {
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    height: -webkit-fill-available;
    // object-fit: fill;
    position: absolute;
    top: 0;
    left: 0;
  }

  .local {
    width: 40%;
    height: 17%;
    position: fixed;
    right: 10px;
    top: 10px;
    z-index: 2;
    border: 1.5px solid #ffff;
    border-radius: 12px;

    @include md {
      width: 190px !important;
      height: 200px !important;
    }

    @include mdl {
      width: 190px !important;
      height: 200px !important;
    }

    @include lg {
      width: 190px !important;
      height: 200px !important;
    }

    @include xl {
      width: 190px !important;
      height: 200px !important;
    }

    @include xxl {
      width: 190px !important;
      height: 200px !important;
    }
  }

  .cus {
    border: 10px solid green;
  }

    .agent {
        // border: 10px solid blue !important;
        width: 100% !important;
        -o-object-fit: cover !important;
        object-fit: cover !important;
        height: 100% !important;
        /* object-fit: fill; */
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        video{
            height: 100%;
        }
    }




}

.app {
  position: relative;
  // border: 1px solid grey;
  width: 70%;
  // min-height: 400px;
  min-height: 100vh;
  background-color: hsla(0, 0%, 100%, 0.95);
  // box-shadow: 10px 10px 20px rgba(80, 79, 79, 0.164);
  border-radius: 1px;
  overflow: hidden;

  &.large-device-app {
    width: 100% !important;
    background-color: transparent !important;

    .instructions-list {
      list-style-type: decimal;
      
    }
  }

  // overflow-y: auto;
  @include xsm {
    width: 100%;
  }

  .app-header {
    min-height: 55px;
    // background-color: rgb(238, 238, 238);
    // background-color: #fff;
    position: sticky;
    top: 0;

    &.app-header-dark {
      background-color: #000 !important;
    }

    @extend .center;

    .app-goback {
      position: absolute;
      left: 8%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-color: #fff;
      width: 34px;
      height: 34px;
      // box-shadow: 4px 4px 15px rgba(85, 85, 85, 0.37);
      border: none;
      border-radius: 50%;
      font-size: 24px;
      cursor: pointer;
      outline: none;
      transition: all 350ms ease;
      z-index: 9999;

      &:hover {
        box-shadow: 4px 4px 15px rgba(85, 85, 85, 0.37);
      }

      img {
        width: 14px;
        height: auto;
        object-fit: cover;
        position: relative;
        top: -2px;
        transform: rotate(180deg);
      }
    }

    .app-header-title {
      color: $appTitleClr;
      font-size: 20px;
      font-family: ProximaNova-semibold;
      margin: 20px;
      text-align: center;
      // padding-left: 20px;
    }
  }


  .tc-merged-screen{
    padding: 2% 4% 6% 4%;
    // margin-bottom: 40px;
    // background-color: red;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    position: relative;
    z-index: 10;
    height: calc(100vh - 80px);
  }

  .app-body {
    padding: 2% 8% 6% 8%;
    // margin-bottom: 40px;
    // background-color: red;
    height: calc(100vh - 55px);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    position: relative;
    z-index: 10;

    @include xsm {
    height: calc(100vh - 160px);
    }


    .main-heading {
      color: #000000;
      text-align: center;
      font-family: ProximaNova-semibold;
      margin-block: 7%;
      font-size: 40px;
    }

    .app-start {
      flex: 1;

      &.l-app-start {
        display: flex;
        align-items: center;
        justify-content: center;

        // flex: inherit;
        .bgWhite {
          background-color: hsla(0, 0%, 100%, 0.82);
          padding: 3%;
          border-radius: 6px;
        }
      }

      .languages-list {
        list-style: none;
        padding-left: 0;
        margin: 0;

        // height: 100%;
        // background-color: rgb(197, 197, 197);
        .languages-list-item {
          color: #121212;
          padding: 12px 0px;
          font-family: ProximaNova-semibold;
          font-size: 20px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          position: relative;
          cursor: pointer;
          transition: all 400ms ease;

          &:hover {
            background-color: rgba(240, 240, 240, 0.445);
          }

          &:nth-last-child(1) {
            border-bottom: 0px;
          }

          // &.active {
          //     @extend .txt-color;
          // }

          .languages-list-check {
            position: absolute;
            right: 0%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }

    .app-end {
      // background-color: rgb(185, 185, 185);
      padding: 4% 0%;
    }

    .app-adhaar-upload {
      display: flex;
      align-items: center;
      border: 1px solid #38a1f7;
      border-radius: 5px;
      position: relative;
      padding: 13px;

      .upload-file {
        position: absolute;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        // background-color: red;
        cursor: pointer;
        color: transparent;

        &::-webkit-file-upload-button {
          visibility: hidden;
        }
      }

      .app-adhaar-upload-content {
        flex: 1;
        // background-color: #38A1F7;
        margin-right: 16%;

        .title2 {
          margin-bottom: 2px;
          font-size: 16px;
          font-family: ProximaNova-semibold;
          color: #000000;
        }

        .txt {
          margin: 0;
          color: #121212;
          font-size: 12px;
        }
      }
    }

    .banner {
      width: 100%;
      margin-bottom: 15%;
      text-align: center;

      img {
        width: 100%;
        height: auto;
      }
    }

    .app-body-img {
      position: relative;
      text-align: center;
      width: 150px;
      height: 150px;
      // background-color: rgba(190, 239, 248, 0.486);
      // border-radius: 50%;
      margin: 0 auto;
      margin-bottom: 10%;
      @extend .center;

      img {
        width: 80%;
        height: auto;
        object-fit: contain;
      }
    }

    .app-body-imgs {
      position: relative;
      text-align: center;
      width: 150px;
      height: 150px;
      // background-color: rgba(190, 239, 248, 0.486);
      // border-radius: 50%;
      margin: 0 auto;
      @extend .center;

      img {
        width: 80%;
        height: auto;
        object-fit: contain;
      }
    }

    .app-body-data {
      text-align: center;
      padding: 3% 0%;
    }

    .app-body-steps {
      list-style: none;
      padding-left: 0;
      margin-bottom: 10%;

      li {
        margin-bottom: 45px;
        display: flex;
        align-items: center;
        position: relative;

        &:nth-last-child(1) {
          margin-bottom: 0;

          &::before {
            display: none;
          }
        }

        &::before {
          content: "";
          position: absolute;
          left: 14px;
          // top: 50px;
          top: 60px;
          transform: translate(-50%, -50%);
          width: 2px;
          height: 30px;
          background-color: #ececec;
        }

        .step-nbr {
          width: 28px;
          height: 28px;
          background-color: #ececec;
          margin-right: 6px;
          font-size: 12px;
          font-family: ProximaNova-Bold;
          border-radius: 50%;
          @extend .center;
        }

        .step-name {
          margin-bottom: 0;
          // line-height: 1;
          font-size: 14px;
        }
      }
    }

    .camera {
      width: 100%;
      height: auto;
      margin-bottom: 10%;

      #video {
        width: 100%;
        height: 100vh !important;
        height: auto;
        border: 2px solid grey;
      }
    }

    .capture-btn {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: #7e7e7e;
      outline: 4px solid #7e7e7e;
      border: 2px solid #fff;
      cursor: pointer;
      margin: 5% 0%;
    }

    .output {
      width: 100%;

      img {
        width: 100%;
        height: auto;
      }
    }

    .app-body-select-doc {
      margin-bottom: 10%;
      margin-top: 10%;

      .select-doc-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #16161e;
        // background-color: #ececec;
        padding: 10px 0px;
        border-top: 1px solid #ececec;
        border-bottom: 1px solid #ececec;
        transition: all 350ms ease;
        position: relative;
        z-index: 1;

        &::before {
          content: "";
          position: absolute;
          top: 0%;
          left: 0%;
          width: 0%;
          height: 100%;
          background-color: #ececec;
          z-index: -1;
          transition: all 350ms ease;
        }

        &:hover {
          text-decoration: none;

          &::before {
            width: 100%;
          }
        }

        figure {
          display: flex;
          align-items: center;
          margin: 0;

          img {
            width: 30px;
            object-fit: contain;
            height: auto;
            margin-right: 17px;
          }
        }

        .btn-select-doc {
          bottom: 0;
          height: 30px;
          // background-color: #ececec;
          // border: none;
          background-color: #fff;
          width: 34px;
          height: 34px;
          box-shadow: 4px 4px 15px rgba(85, 85, 85, 0.192);
          border: none;
          border-radius: 50%;
          font-size: 24px;
          cursor: pointer;
          outline: none;

          img {
            width: 20px;
            height: auto;
            object-fit: cover;
            position: relative;
            top: -2px;
          }
        }
      }
    }

    .app-display-pic {
      width: 100%;
      height: 190px;
      border: 1px solid rgb(168, 168, 168);
      border-radius: 4px;
      text-align: center;
      margin-bottom: 20%;
      margin-top: 10%;

      &.selfie-video {
        height: 340px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .display-pic-txt {
      margin-bottom: 20%;
    }
  }

  // .app-footer{
  //     position: absolute;
  //     bottom: 0;
  //     height: 40px;
  //     border-top: 1px solid rgb(202, 202, 202);
  //     width: 100%;
  //     font-family: ProximaNova-Bold;
  //     color: #4d4d4d;
  //     @extend .center;
  //     .app-footer-txt{
  //         color: #7e7e7e;
  //         font-family: ProximaNova-Regular;
  //         font-size: 12px;
  //         margin-right: 3px;
  //     }
  // }
}

.video-guide {
  padding-left: 10px;
  margin: 0;
  font-size: 15px;
  // margin-bottom: 40%;
  // list-style: outside url('../images/icon-list.svg');
  // list-style-image: url('../images/icon-list.svg');
  list-style: none;

  li {
    margin-bottom: 15px;
    display: flex;
    // justify-content: space-between;

    // background: url('../images/icon-list.svg') no-repeat;
    // background-position: 7px center;
    // &::marker{
    //     color: #38A1F7;
    // }
    .identity-names {
      font-family: ProximaNova-semibold;
      margin: 0;
      color: #121212;
      font-size: 18px;

      // text-decoration: none;
      &:hover {
        text-decoration: none;
      }
    }
  }
}

.cus-consentBtn {
  width: 90%;
  margin: 0 auto;
  position: fixed;
  bottom: 6px;
  border-radius: 6px;
  left: 5%;
}

.dark {
  background-color: #16161e;

  .app-header {
    background-color: #16161e;

    .app-header-title {
      color: #fff;
    }
  }
}

.app-body-frm {
  padding: 3% 0%;
}

.frm-grp {
  position: relative;
  margin-bottom: 14px;

  .frm-grp-inp {
    width: 100%;
    height: 45px;
    border: 1px solid rgb(168, 168, 168);
    color: #16161e;
    // color: #000;
    border-radius: 6px;
    padding: 0px 10px;
    font-family: ProximaNova-Bold;
  }
}

.secret-codes {
  margin-block: 2%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .scretet-inp {
    @extend .txt-color;
    width: 40px;
    border: 0;
    border-bottom: 3px solid #38568f;
    outline: none;
    padding: 2px;
    background-color: grey;
    margin: 10px;
    font-family: ProximaNova-Bold;
    font-size: 26px;
    text-align: center;
  }
}
.secret-code {
  justify-content: center;
  gap: 1rem;
}

.otp-txt {
  color: rgb(75, 109, 224);
  display: block;
  text-align: center;
  margin-top: 15px;
  font-size: 14px;

  &:hover {
    text-decoration: none;
  }
}

.custom-control-input:checked ~ .custom-control-label::before {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
    linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
}

.custom-dialog-end {
  @include xsm {
    align-items: flex-end !important;
    margin: 0;
    min-height: calc(100%) !important;
  }

  @include sm {
    align-items: flex-end !important;
    margin: 0;
    min-height: calc(100%) !important;
  }

  &.modal-dialog {
    @include xsm {
      max-width: 100% !important;
    }

    @include sm {
      max-width: 100% !important;
    }
  }

  .modal-content {
    border-radius: 25px 25px 0px 0px;
  }
}

.spinner {
  position: fixed;
  // left: 50%;
  // top: 50%;
  // transform: translate(-50%, 50%);
  text-align: center;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background-color: #36363642;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  // background-color: rgba(0, 0, 0, 0.093);
  // width: 100vh;
  // height: 100vh;
  // z-index: 99;
}

.spinner > div {
  width: 25px;
  height: 25px;
  background-color: #38a1f7;

  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
  }
}

@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

.aadhar-details {
  .cus-textareaKYC textarea {
    height: 100%;
    padding-top: 2rem;
    border-top: solid #e9ecef 1.5rem !important;
    border-radius: 0;

    label {
      z-index: 1;
    }
  }
}

.ocr-pht {
  width: 100px;
  height: 100px;
  border: 3px solid #0dcaf06e;
  border-radius: 3px;
  margin-bottom: 10px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.cus-ocrInput {
  position: relative;

  .cus-PencilIcon {
    position: absolute;
    font-size: 10px;
    top: 50%;
    right: 5%;
    opacity: 0.5;
  }
}

.errorTxt {
  position: absolute;
  color: #ff002f;
  margin-bottom: 1rem;
  font-size: 0.8rem;
}

.filename-display {
  position: absolute;
  top: 25%;
  opacity: 1;
  left: 4px;
  font-size: 15px;
  display: inline-block;
  width: 80%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}

.fileicon-display {
  position: absolute;
  font-size: 10px;
  top: 50%;
  opacity: 1;
  transform: translate(-50%, -50%);
  right: -15px;
  border-radius: 5px;
  padding: 3px;
  width: 32px;
  height: 32px;
  z-index: -1;
  @extend .center;

  img {
    width: 17px;
  }
}

.spin-loader {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.display-qtn {
  position: absolute;
  text-align: center;
  width: 100%;
  bottom: 16%;
  left: 0;
  // transform: translate(0%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 99999;

  span {
    width: 90%;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #ffffff;
  }
}

.upload-btn-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;

  .Pan_Cap_heading {
    font-size: 20px;
  }

  .cus-pan_proceed {
    color: #ffffff;
  }
}

.upload-btn-pan {
  border: 2px solid gray;
  color: gray;
  background-color: white;
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 20px;
  font-weight: bold;
}

.upload-btn-wrapper input[type="file"] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

.upload-displayimg {
  object-fit: cover;
  position: absolute;
  bottom: 0px;
}

.display-btn {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 40px;
  width: 100%;
  padding: 20px;
}

.capture-photo {
  // -webkit-background-clip: text;
  // -webkit-text-fill-color: transparent;
  // background-clip: text;
  font-size: 18px;
  font-weight: 400;
  color: #ffffff;

  // img {
  //     object-fit: cover;

  // }
}

.text-wrapping {
  border: 1px solid;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
    linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  padding: 4px;
  width: 120px;
  display: inline-block;
  border-radius: 6px;
  // display: inline-block;
  // width: 180px;
  // white-space: nowrap;
  // overflow: hidden !important;
  // text-overflow: ellipsis;
}

.tidepanbtn {
  background: transparent;
  border-radius: 256px;
  height: 44px;
  font-weight: bold;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.5px;
  border: none;
  margin-top: 10px !important;
  color: #1929d6 !important;
}

.panimg {
    // border: 2px solid black;
    border-radius: 10px;
    // border: 1.5px solid rgb(1, 139, 126);
    width: auto;
    height: 250px;
    object-fit: contain;
    object-position: center;
    // background: rgba(51, 51, 51, 0.7);
border: 0.712766px solid #D0D0D0;
}
.panimg1 {
    // border: 2px solid black;
    padding: 30px;
    border: 1.5px solid rgb(1, 139, 126);
    height: 300px;
    // background: rgba(51, 51, 51, 0.7);
  // border: 2px solid black;
  border-radius: 10px;
  // border: 1.5px solid rgb(1, 139, 126);
  width: auto;
  height: 250px;
  object-fit: contain;
  object-position: center;
  // background: rgba(51, 51, 51, 0.7);
  border: 0.712766px solid #d0d0d0;
}

@media (min-width: 991.98px) {
  .panimg {
    width: 50%;
  }
}

.video-play {
  width: 100%;
  object-fit: cover;
  height: -webkit-fill-available;
  /* object-fit: fill; */
  position: absolute;
  top: 0;
  left: 0;
}

// WARNING POPUP
.warning-msg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;

  .warning-msg-bx {
    width: 240px;
    height: 300px;
    border-radius: 14px;
    background: rgba(242, 242, 242, 0.8);
    display: flex;
    align-content: space-between;
    flex-direction: column;

    @include xsm {
      width: 90%;
    }
  }

  .warning-msg-bx-top {
    flex: 1;
    padding: 4%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .warning-msg-bx-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 14px;
  }

  .warning-msg-btn {
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
  }
}

.timer {
  position: absolute;
  top: 13px;
  left: 12px;
  padding: 2px 14px 2px 15px;
  background-color: rgb(242, 78, 30);
  color: #ffffff;
  z-index: 2;
  // z-index: 99999;
  border-radius: 10px;
}

.ovel-canvas {
  position: absolute;
  top: 0%;
  left: 0%;
  -o-object-fit: cover;
  object-fit: cover;
  // background: url(http://localhost:3000/images/Ovel-canvas.svg) no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  height: 100vh;
  width: 100%;

  // z-index:1;

  .ovel-canvas-img {
    // position: absolute;
    // /* top: 0%; */
    // /* left: 0%; */
    // object-fit: cover;
    // /* background-size: cover; */
    // height: 100vh;
    // width: 100%;
    position: absolute;
    top: 0%;
    left: 0%;
    -o-object-fit: cover;
    object-fit: cover;
    background-size: cover;
    height: 100vh;
    width: 100%;
    z-index: 1;

    // z-index: 1;
    @include md {
      -o-object-fit: initial;
      object-fit: initial;
    }

    @include mdl {
      -o-object-fit: initial;
      object-fit: initial;
    }

    @include lg {
      -o-object-fit: initial;
      object-fit: initial;
    }

    @include xl {
      -o-object-fit: initial;
      object-fit: initial;
    }

    @include xxl {
      -o-object-fit: initial;
      object-fit: initial;
    }
  }
}

.cus-PencilIcon {
  position: absolute;
  font-size: 10px;
  top: 50%;
  right: 5%;
  opacity: 0.5;
}

// .dot {
//     background: -moz-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 150px);
//     background: -webkit-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 150px);
//     background: -ms-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 150px);
//     background: -o-radial-gradient(transparent 150px, rgba(0, 0, 0, 1) 150px);
//     pointer-events: none;
// }
.display-img {
  position: relative;
  z-index: 9;
  padding: 20px 20px 0 20px;
}

.pan_bg {
  background-color: black;
  // height: 100vh;
  padding: 20px;
  // height: calc(100vh - 55px);
  // @extend .app;
  position: relative;
  z-index: 9;

  @include xsm {
    height: calc(100vh - 100px);
  }

  .video_pan {
    margin: 50px 0;
    width: 100%;
    height: 220px;
    background: #323131;
    border: 1.5px solid #ffffff;
    border-radius: 8px;

    .video {
      width: 100%;
      // height: 100%;
      height: 250px !important;
      border-radius: 8px;
      object-fit: cover;

      // object-fit: cover;
      // @media (max-width: 536px) {
      // }
    }

    .canvas {
      display: none;
    }
  }

  .VerificationTextStyled {
    color: #fff;
    margin-top: 20px;
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    text-align: center;
    margin-bottom: 0px !important;
  }

  .VerificationSmallTextStyled {
    margin-top: 4px !important;
    font-style: normal;
    font-size: 18px;
    text-align: center;
    color: #e9e5e5;
    opacity: 0.6;
    padding: 0 30px;
  }
}

.camera-flex {
  display: flex;
  justify-content: center;

  .camera-div {
    position: absolute;
    bottom: 0px;
    width: 90%;
    color: white;

    .camera-btn {
      display: flex;
      justify-content: center;

      .camera-svg {
        &:active {
          padding: 0px 0px 1px 0px;
        }
      }
    }

    .camera-content {
      display: flex;
      justify-content: space-between;
      margin: 20px 0px;
    }
  }
}

.cstmr-brwsr-dtls-mn {
  display: flex;
  align-items: center;
  justify-content: center;
  // height: 100%;
  min-height: 100vh;
  width: 100%;
  animation: down 400ms linear;
  position: relative;
  z-index: 999;
  background: #e5e5e5;
}

.cstmr-brwsr-dtls {
  position: relative;
  padding: 6% 3%;
  width: 85%;
  // background: url('../images/browserbg.png') rgba(255, 255, 255, 0.6);;
  background: #ffffff;
  // background-size: contain;
  // background-repeat: no-repeat;
  // background-position: 0 0;
  // background-blend-mode: multiply;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  z-index: 99;

  .browserbg-img {
    position: absolute;
    left: 10%;
    top: 10%;
    z-index: -1;
  }

  // @include xsm{
  //     width: 100%;
  // }
  .cstmr-brwsr-dtls-ttl {
    color: #000000;
    text-align: center;
    margin-bottom: 10px;
    margin-top: 20px;
    font-size: 20px;
    line-height: 1.5;
  }

  .cstmr-brwser-dtls-copytxt {
    color: #3799ff;
    display: inline-block;
    // width: 80%;
    padding-right: 10px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    font-weight: bold;
  }

  .cstmr-brwsr-dtls-sbttl {
    color: rgb(78, 78, 78);
    margin-bottom: 10px;
    text-align: center;
    font-size: 15px;
  }

  .cstmr-brwsr-dtls-bx {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    // width: 150px;
    min-height: 162px;
    // border-bottom: 1px solid rgb(198, 198, 198);
    // margin-bottom: 10px;
    background: #ffffff;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.25);
    border-radius: 6px;
    // &:nth-last-child(1){
    //     border-bottom: none;
    // }

    .cstmr-brwsr-dtls-bx-img {
      width: 40px;
      height: 40px;
      border-radius: 2px;
      // border: 1px solid rgb(198, 198, 198);
      margin-bottom: 10px;

      img {
        object-fit: cover;
      }
    }

    .cstmr-brwsr-dtls-bx-cntnt {
      flex: 1;
      text-align: center;

      .cstmr-brwsr-dtls-bx-ttl {
        margin-bottom: 4px;
        font-size: 16px;
        font-weight: bold;
      }

      .cstmr-brwsr-dtls-bx-txt {
        color: rgb(40, 39, 39);
        margin-bottom: 3px;
        font-size: 14px;

        span {
          color: #018b7f;
          font-weight: bold;
        }
      }
    }
  }
}

@keyframes down {
  0% {
    transform: translateY(50px);
    opacity: 0.5;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.themeBtn {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
    linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
  color: #ffffff;
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 2px 12px;
}

.themeBtn:active {
  transform: scale(0.98);
  /* Scaling button to 0.98 to its original size */
  box-shadow: 3px 2px 22px 1px rgba(0, 0, 0, 0.24);
  /* Lowering the shadow */
}

.modalAudioVideoCheck {
  background: #ffffff;
  border-radius: 5px;
  padding: 34px 18px;
}

.heading {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
  color: #000000;
}

.para {
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 17px;
  color: #000000;
}

.videoPreview {
  font-style: normal;
  font-weight: 400;
  font-size: 17px;
  line-height: 20px;
  color: #121212;
  opacity: 0.6;
}

.checkVideo {
  box-sizing: border-box;
  position: absolute;
  // width: 275px;
  // height: 282px;
  // left: 50px;
  // top: 239px;
  background: #333333;
  border: 0.6px solid #d0d0d0;
  border-radius: 5px;
  background-image: url("images/checkAudiovideo.png");
}

.audioCheck {
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 20px;
  color: #121212;
  opacity: 0.6;
}

.ppBackground {
  position: absolute;
  width: 22px;
  height: 22px;
  background: #d9d9d9;
  border-radius: 5px;
}

#container-circles {
  bottom: 125px !important;
  position: fixed !important;
  background-color: black !important;
}

// CHAT BOX CSSS-------------------------------------------------------------------------

.chat {
  width: 100%;
  height: auto;
  position: relative;

  .chat-box {
    width: 100%;
    min-height: 200px;
    height: 70vw;
    max-height: 370px;
    background-color: #ffffff;
    overflow-y: scroll;
    padding: 0.8rem;

    &::-webkit-scrollbar {
      width: 0px;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.137);
      // outline: 1px solid slategrey;
      border-radius: 6px;
    }

    .message-box {
      display: flex;
      margin-bottom: 3%;
      font-family: ProximaNova-Regular;

      p:first-child {
        margin: 0px;
        padding: 2px;
        word-break: break-all;
      }

      p:nth-child(2) {
        color: #666666;
        font-weight: 400;
        font-size: 9px;
        line-height: 24px;
        margin-bottom: -8px !important;
        position: relative;
        float: right;
      }

      // width: 70%;
      &.message-box1 {
        justify-content: flex-end;
        color: white;
      }

      &.message-box2 {
        justify-content: flex-start;
        color: black;
      }

      .bank-msg {
        // background-color: #000000;
        max-width: 80%;
      }

      .user-msg {
        // background-color: #000000;
        max-width: 80%;
      }

      .media {
        align-items: flex-end;
      }

      .media-body {
        border-radius: 10px;
        padding: 5px 10px;

        &.chat-left {
          font-weight: 400;
          font-size: 14px;
          line-height: 17px;
          color: #333333;
          background: #e7f4ff;
          border-radius: 0px 8px 8px 8px;
        }

        &.chat-right {
          font-weight: 400;
          font-size: 14px;
          line-height: 17px;
          background: linear-gradient(
              0deg,
              rgba(0, 0, 0, 0.2),
              rgba(0, 0, 0, 0.2)
            ),
            linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
          border-radius: 8px 0px 8px 8px;
        }

        .chat-msg {
          font-weight: 400;
          font-size: 14px;
          line-height: 17px;
        }

        .chat-right .chat-msg {
          color: #ffffff;
        }

        .chat-time {
          font-weight: 400;
          font-size: 9px;
          line-height: 24px;
          color: #666666;
        }
      }
    }
  }

  .chat-form {
    font-family: ProximaNova-Regular;
    width: 100%;
    padding-bottom: 4px;

    // float:center;
    .chat-inp {
      width: 97%;
      display: block;
      background-color: white;
      height: 3rem;
      outline: none;
      padding: 0.5rem;
      color: black;
      border: 0.6px solid #c1c1c1;
      border-radius: 8px;
      margin-left: 5px;
      margin-top: -1px;
      padding-right: 2.2rem;
    }

    .chat-icon {
      position: absolute;
      top: 11px;
      color: black;
      background-color: transparent;
      border: 0px;
      padding: 0;
      cursor: pointer;
      outline: none;

      &.icon1 {
        left: 10px;
      }

      &.icon2 {
        right: 1rem;
        top: 0.875rem;
      }
    }
  }
}

p.chat-time.text-left {
  float: left !important;
}

.bot {
  position: fixed;
  bottom: 5%;
  right: 4%;
  z-index: 998;
  // box-shadow: 0px 7px 8px 0px rgb(0 0 0 / 16%);
  // border-radius: 8px;

  .chat-bot {
    position: fixed;
    bottom: 3%;
    right: 4%;
    width: 8vh;
    height: 8vh;
    border-radius: 200px;
    //   box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.1);
    // display: flex;
    // align-items: center;
    // justify-content: center;
    border: 0px;
    cursor: pointer;
    background: linear-gradient(180deg, #38568f 0%, #38a1f7 100%) !important;
    z-index: 9999;
    outline: none;
    color: #ffffff;

    &.botactive {
      background-color: #1a488d;
    }
  }

  .bot-box {
    width: 90vw;
    //   height: 58vh;
    max-width: 360px;
    //   color:#018b7e
    //   margin-bottom: 70px;
    border-radius: 8px;
    //   box-shadow: 0px 0px 20px rgba(31, 31, 31, 0.3);
    // overflow: hidden;
    z-index: 9999;
    display: none;

    // transition: all 450ms linear;
    // animation: show 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
    &.chatshow {
      display: block;
      // transition: all 450ms linear;
      // animation: show 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
    }

    .bot-header {
      width: 100%;
      height: auto;
      padding: 10px;
      background-color: #fff;
      display: block;
    }

    .info-title {
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
        linear-gradient(269.82deg, #38a1f7 0.1%, #38568f 99.85%);
      padding: 14px;
      color: #ffffff;
      border-radius: 8px 8px 0px 0px;
      margin-bottom: 0;
      height: 3rem;
      font-size: 16px;
      letter-spacing: 1px;
      align-items: center;
    }

    .infoicon {
      position: relative;
      float: right;
    }
  }
}

.close-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 400ms cubic-bezier(0.6, -0.28, 0.735, 0.045);
  animation: chatclose 0.9s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.chatcmp {
  position: relative;
  background: red;
  // box-shadow: 0px 3px 3px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
}

.inpCmp {
  background-color: #fff;
  border-radius: 0 0 0.5rem 0.5rem;
}

.notify-msg {
  width: 8vh;
  height: 8vh;
  background: #20a47e;
  position: absolute;
  right: 5%;
  top: 4%;
  z-index: -1;
  border-radius: 100px;
  display: none;
  animation: notify 1s linear infinite alternate;

  &.notifyactive {
    display: block;
  }
}

@keyframes show {
  0% {
    transform: scale(0);
    transform-origin: 100% 100%;
    opacity: 0;
  }

  100% {
    transform: scale(1);
    transform-origin: 100% 100%;
    opacity: 1;
  }
}

@keyframes showin {
  0% {
    transform: scaleY(1);
    transform-origin: 0% 100%;
    opacity: 1;
  }

  100% {
    transform: scaleY(0);
    transform-origin: 0% 100%;
    opacity: 1;
  }
}

@keyframes chatclose {
  0% {
    transform: scaleY(0, 0);
    opacity: 0;
  }

  100% {
    transform: scaleY(1.1, 1.1);
    opacity: 1;
  }
}

@keyframes notify {
  0% {
    //   transform: rotate(0deg) translateX(90px) rotate(0deg) scale(1);;
    opacity: 0;
    transform: scale(1);
  }

  100% {
    // transform: rotate(360deg) translateX(90px) rotate(-360deg) scale(1.2);;
    opacity: 1;
    transform: scale(1.2);
  }
}

//   .emojeeBox{
//     width: 340px;
//     height: 410px;
//     position: absolute;
//     right: 340px;
//     bottom: -90px;
//   }

.videobtn {
  margin-top: 10px;

  // color: rgb(255, 255, 255);
  // font-size: 16px;
  width: 100%;
  height: 36px;
  // padding: 10px 30px 10px 30px;
  background: rgb(1, 139, 126);
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tidevideobtn {
  // margin-top: 40px;

  // color: rgb(255, 255, 255);
  // font-size: 16px;
  width: 100%;
  height: 36px;
  // padding: 10px 30px 10px 30px;
  background: rgb(1, 139, 126);
  font-family: ProximaNova-semibold;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.locationtideenablemsg {
  font-weight: 500;
  font-size: 12px;
  color: #2f3037;
  letter-spacing: 0.4px;
  margin-left: 8px;
  margin-top: -20px;
}

.warning-popup {
  position: absolute;
  min-width: 40%;
  height: auto;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 4px 4px solid rgba(222, 222, 222, 0.097);
  background-color: #16161ea1;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  z-index: 9999;
}

//Ip validations
.ipdata {
  padding: 3px;
}

.tableContainer {
  width: 90%;
  margin: 0 auto;
}

.changeNtwkImg {
  width: 100%;
  text-align: center;
  margin-bottom: 20px;
}

.changeNtwkTxt {
  width: 100%;
  font-weight: 600;
  font-size: 20px;
  letter-spacing: 0.5px;
  margin-bottom: 20px;
  color: #ff815c;
  opacity: 0.8;
  text-align: center;
}

.connectNtwk {
  text-align: center;
  color: #121212;
  opacity: 0.6;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 20px;
}

.ioserr {
  text-align: center;
  color: red;
  opacity: 0.6;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 20px;
}

.closebutton {
  // position: relative;
  // left: 93%;
  width: 100%;
  padding: 5px;
  text-align: end;
}

.keys {
  width: 23px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #333333;
  border-radius: 8px 0 0 0;
}

.values {
  width: 23px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #333333;
  border-radius: 0 8px 0 0;
}

.fontBold {
  font-weight: bold !important;
  margin-top: 9%;
}

.inspectBtn {
  height: 45px;
  outline: none;
  border-radius: 6px;
  border: 0;
  transition: all 300ms;

  .trans-img {
    padding: 0px;
    text-align: center;
    filter: invert(1);
    width: 24px;
    padding-left: 12px;
  }
}

.table > :not(:first-child) {
  border-top: none;
}

.pan_bg_tide {
  position: relative;
  height: 100vh;
  background-color: black;
  overflow-y: scroll;

  .panbgarrowicon {
    position: absolute;
    top: 40px;
    left: 20px;
    border: none;
    background-color: transparent;
  }

  .react-html5-camera-photo > video {
    width: 90% !important;
    border: 1.5px solid #ffffff;
    border-radius: 16px;
    height: 250px !important;
    margin-top: 100px;
  }
  .camerapositioninfo {
    display: flex;
    flex-direction: column;
    padding: 20px;
    margin-bottom: 120px;
    .camerapositioninfomain {
      display: flex;
      margin-top: 10px;
      // flex-direction: column;
      img {
        margin-top: 1px;
        margin-right: 10px;
        width: 20px;
        height: 20px;
      }
      .camerapositioninfotext {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0.25px;
        color: #ffffff;
        word-wrap: wrap;
      }
    }
  }
}

.tidevideocalllangheading {
  margin-top: 60px;
  font-weight: 500;
  color: #0c0c0e;
  font-size: 24px;
  line-height: 32px;
}
.tidevideocalllanginfo {
  margin-top: 10px;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  margin-bottom: 20px;
}

.tidelangopt {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  // box-shadow: 0 0px 8px 0 rgba(0,0,0,0.1);
  transition: 0.3s;
  padding: 16px;
  margin-bottom: 2px;
  background: #ffffff;
  // border-radius: 8px;
  &:nth-child(1) {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  &:nth-last-child(1) {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

.tidelangarrow {
  width: 17px;
  height: 13px;
  float: right;
  margin-top: 5px;
}

.tideloactionperheader {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #343434;
  margin-top: 100px;
}

.tideloactionperheaderinfo {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
  color: #5f606d;
  margin-top: 16px;
}

.tidelocationimg {
  margin-top: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tidecalope {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.kycstatusheader {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #0c0c0e;
}

.kycstausinfo {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  margin-top: 10px;
}
.kycstausimg {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
  // width: 70%;
}
.kycsubmitstaus {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.tideheadermain {
  padding: 10px 0 0 30px;
}
.tideheaderbutton {
  margin-top: 15px;
  border: none;
  background-color: transparent;
  /* width: 21px;
    height: 15px; */
  margin-bottom: 10px;
}
.tidearrowicon {
  width: 21px;
  height: 15px;
}
.tideheadertext {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #343434;
}
.tidetokennumbertime {
  font-weight: 400;
  font-size: 34px;
  line-height: 40px;
  letter-spacing: 0.25px;
  color: #1929d6;
}
.tidetokennumbertitle {
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  color: #0c0c0e;
}
.tidetokenpageadjust {
}
.bookaslotinfo {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  display: flex;
  align-items: flex-end;
  letter-spacing: 0.25px;
  color: #0c0c0e;
}
.tidetokennumber {
  font-weight: 400;
  font-size: 34px;
  line-height: 40px;
  letter-spacing: 0.25px;
  color: #1929d6;
}
.tidewaitingtxt {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  margin-top: 8px;
  margin-bottom: 8px;
}
.tidewaitinginfo {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  // margin-top: 20px;
}

.tidewaitinginfos {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
  // margin-top: 20px;
  margin-bottom: 50px;
}
.reschedulecalendertitle {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  color: #0c0c0e;
}

.tidecalender {
  box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  margin-top: 40px;
}

.react-calendar {
  border-radius: 8px !important;
}

.selecttimelableforbookslot {
  margin-top: 32px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.1px;
  color: #0c0c0e;
}

.selecttimeforbookslotmain {
  // padding: 16px 12px 16px 16px;
}

.selecttimeforbookslot {
  width: 100%;
  margin-top: 10px;
  gap: 8px;
  box-shadow: 0 0px 8px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #e4e4e7;
  padding: 16px 12px 16px 16px;
  background-position: right 0.75rem center;
  margin-bottom: 100px;
}

.tideadhardetails {
  padding: 20px;
}

@media (max-width: 575.98px) {
  .bookslotbuttonposition {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}

@media (max-width: 600px) {
  .panverifybutton {
    position: fixed;
    bottom: 2%;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px;
    left:50%;
    transform: translate(-50%, -2%);
    z-index: 9999;
  }
}
@media (min-width: 600px) and (max-width: 1200px) {
  .panverifybutton {
    position: fixed;
    bottom: 2%;
    display: flex;
    flex-direction: column;
    width: 70%;
    padding: 20px;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }
}
.selectlangbtn {
  position: fixed;
  bottom: 3.5%;
  padding: 0 30px 0 30px;
  width: 100%;
  left: 50%;
  padding-bottom: 4%;
  transform: translate(-50%, 0%);
  z-index: 9999;
}
@media (max-width: 575.98px) {
  .homepagebutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 3.5%;
    // padding-bottom: 2%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
    z-index:9999;
  }
  .selectkycbutton{
    position: fixed;
    padding: 0 30px 100px 30px;
    bottom: 2%;
    width: 100%;
    left: 50%;
    height: 50px;
    transform: translate(-50%, 30%);
    z-index: 9999;
  }
}

@media (max-width: 575.98px) {
  .panbutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}

@media (max-width: 575.98px) {
  .joinnowbtntide {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}

@media (max-width: 575.98px) {
  .kyccombtn {
    position: fixed;
    padding: 10px 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
    z-index: 9999;
  }
}

.agentunavailableimg {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 78px;
}

.agentunavailabletxt {
  margin-top: 34px;
  font-weight: 600;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
  color: #000000;
}

.agentunavailableinfo {
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  text-align: center;
  color: #6e6e6f;
  margin-top: 16px;
}

@media (max-width: 575.98px) {
  .userconsentbutton {
    position: fixed;
    // bottom: 10%;
    left: 50%;
    transform: translate(-50%, 2%);
    width: 90%;
    background-color: rgba(255, 255, 255, 0.9490196078);
    // height: 20%;
    overflow: hidden;
    bottom: 0;
    height: 240px;
  }
}
@media (max-width: 575.98px) {
  .kycbutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

@media (max-width: 575.98px) {
  .pankycbutton {
    position: fixed;
    padding: 0 30px 0 30px;
    bottom: 0%;
    width: 100%;
    left: 50%;
    transform: translate(-50%, 0%);
    z-index: 9999;
  }
}

@media (max-width: 575.98px) {
  .tidecameraaccessbutton {
    position: fixed;
    bottom:0%;
    padding: 0 30px 0 30px;
    width: 100%;
    left: 50%;
    padding-bottom: 2%;
    transform: translate(-50%, -50%);
  }
}

.tidewaitinginfotxt {
  display: flex;
  margin-top: 16px;
}

.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
  // color: red;
  width: 100%;
  height: 100vh;
  font-weight: bold;
  font-size: 20px;
  flex-direction: column;
  padding: 30px;
  .text1 {
    font-size: 30px;
  }

  .link {
    text-align: center;
    word-break: break-all;
    display: block;
  }
}

.userconsent {
  width: 100%;
  min-height: 100vh;
}

.watermarks {
  position: absolute;
  // left: 50%;
  bottom: 0%;
  opacity: 0.3;
  z-index: 9999999;
  // font-weight: 600;
  font-size: 17px;
  left: 7%;
}

// Image Crop Silder CSS Starts

.slidecontainer {
  width: 100%;
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 5px;
  border-radius: 5px;
  background: #d3d3d3;
  outline: none;
  opacity: 0.7;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
}

.slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: rgb(1, 139, 126);
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: rgb(1, 139, 126);
  cursor: pointer;
}

.cropPageSubHead {
  font-size: 16px;
  font-weight: 400;
}
.PanCardImageText {
  font-weight: 400;
  font-size: 14px;
}
.SliderHederTextCropPage {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  color: #121212;
  opacity: 0.6;
  div {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
  }
}
// Image Crop Silder CSS Ends

.OccupationSelectionForm {
  line-height: 32px;
  font-size: 18px;
  font-weight: 400;
}
input[type="radio"] {
  /* remove standard background appearance */
  -webkit-appearance: none;
  -moz-appearance: none;
  // appearance: none;
  // appearance: auto;
  appearance: auto;
  /* create custom radiobutton appearance */
  width: 17px;
  height: 17px;
  /* background-color only for content */
  background-clip: content-box;
  border: 2px solid gray;
  padding: 2px;
  border-radius: 50%;
  margin-right: 10px;
}

.customerreconnect{
  display: flex;
  justify-content : center;
  align-items : "center";
  position :absolute;
  top:40%
}


.custom-select {
  position: relative;
  display: inline-block;
  min-width: 8rem;


}

.selected-option {
  padding: 10px;
  // border: 2px solid gray;
  border-radius: 5px;
  cursor: pointer;
  min-width: 8rem;
  background: #E3EBF3;
  display: flex;
  justify-content: space-between;
  align-items: center;

}

.options-list {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1;
  list-style-type: none;
  margin: 0;
  padding: 0;
  width: 100%;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top: none;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  font-size: 16px;
  max-height: 180px;
  overflow: scroll;
  li {
    padding: 10px;
    cursor: pointer;
    font-weight: 550;
    text-align: center;
    border-bottom: 1px solid rgb(254, 246, 246);
    // color: green;
    &:hover {
      color: red !important;
    }
  }
 
}

// .options-list li {
//   padding: 10px;
//   cursor: pointer;
//   font-weight: 500;
//   text-align: center;
// }

// .options-list li:hover {
//   color: red;
// }

#canvas {
  display: none;
}
