STATIC DATA
============================

Get details
	-> get details - done
	-> createslot - CreateSlot
select adhar
	-> digi status page -DigiLockerRequest
	-> iframe(url) - DigiLockerRequest - for status(interval)
Success Adhar

Pan with old setup
	-> PanOCR
	-> SavePanData (can edit pan details after ocr)
	-> VerifyPANNumber after edit save data

Select language
	-> InitiateVideoConferenceQueue

token number
	-> InitiateVideoConferenceQueue
		-> isSchedule === 0 
			-> GetUpdatedVCIPIDTokenNumber(bg)
			-> enablebookslot === 1 ->Enable book slot button
				-> GetVideoCallScheduleCalender
		-> isSchedule === 1
			-> GetVideoCallScheduleDetails(bg)
			-> joinstatus === 0 no need to add this condition for cancel call
				-> CancelVideoCallSchedule

isSchedule 0 - call tokenNumber 
		1 - getVideoCallSchedule API

book button
	enablebookslot - 0 and isscheduled - 1 disbaled
	enablebookslot - 1 enable



VIDEO CALL SCREEN










======================================AGENT

GetDashBoardCount(reqtype)
	1- for overall
	0- for today

AgentVcipIdList -loop
	->listtype 	1- live
				2- schedule

AgentCallHistory
	-> isall 	1 - all
				0 - 
	-> fdate, tdate (YYYY-MM-DD)

JoinVideoConferenceSessionID
	-> 
	-> GetVCIPIDDetails - vcipkey

UpdateVCIPIDStatusByAgent 
	-> vcipstatus 
		-> 3 - reject
		-> 2 - approve
	-> agentstatus
		-> 2 -rejcted
		-> 3 - issued
		-> 1 - approve





PushNotifications
	-> notificationid
		-> 1 -qtn and capture for pic
		-> 2 -call-end
		-> 


CheckImageLiveness
	-> 


UpdateMatchStatusByAgent
	-> matchtype 
		-> 1-  kyc live
		-> 2- pan live
	-> matchstatus
		-> 1 - true correct
		-> 2 - false wrong


// ===============************** BUILD STRUCTURE **************===============

INSTALL env-cmd

create .env file in project structure
    .event.dev
        -> inside add below lines
        REACT_APP_ENV="dev"
        BUILD_PATH='./CUSTOMER_DEV'

AFTER THIS BELOW CODE PACKAGE.JSON FOR BUILD
    -> "build:dev": "env-cmd -f .env.dev npm run-script build",


Created to build cmds for prodcurtion(******************************)

-> winBuild is for windows cmd
-> build is for linux cmd

if don't a do winBuild in WINDOWS environment then you will see entire source in the web browser (Source tab)