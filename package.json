{"name": "video-kyc-v1", "version": "2.11.17", "private": true, "dependencies": {"@100mslive/react-sdk": "^0.10.3", "@hyperdx/browser": "^0.21.2", "@openreplay/tracker": "^4.1.5", "@openreplay/tracker-axios": "^3.6.2", "@sentry/react": "^7.53.1", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0", "aes-everywhere": "^1.0.0", "axios": "^0.26.1", "base-64": "^1.0.0", "detect-browser": "^5.3.0", "emoji-picker-react": "^3.6.2", "env-cmd": "^10.1.0", "face-api.js": "^0.22.2", "gleap": "^11.0.6", "livekit-client": "2.8.1", "loggly": "^1.1.1", "mic-check": "^1.1.0", "moment": "^2.29.2", "node-verhoeff": "^0.0.11", "openvidu-browser": "2.25.0", "prop-types": "^15.8.1", "public-ip": "^5.0.0", "react": "^17.0.2", "react-calendar": "^3.7.0", "react-countdown": "^2.3.2", "react-dom": "^17.0.2", "react-hot-toast": "^2.2.0", "react-html5-camera-photo": "^1.5.5", "react-icons": "^5.4.0", "react-image-crop": "^10.0.9", "react-lottie": "^1.2.3", "react-redux": "^7.2.6", "react-router-dom": "6.2.2", "react-scripts": "5.0.0", "react-spinners": "^0.13.4", "react-timecode": "^1.2.0", "react-timer-wrapper": "^0.4.1", "redux": "^4.1.2", "redux-saga": "^1.1.3", "styled-components": "^5.3.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "build-css": "node-sass src/styles -o src/styles", "build:lq_dev_livekit": "env-cmd -f .env.lq_dev_livekit_thomas react-scripts build", "build:dev_tc_livekit": "env-cmd -f .env.tc_dev_livekit react-scripts build", "winBuild": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build", "winBuild:dev_tc_livekit": "env-cmd -f .env.tc_dev_livekit npm run-script winBuild", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}