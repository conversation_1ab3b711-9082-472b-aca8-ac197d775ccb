name: deploy-to-tc-preprod

on:
  workflow_dispatch:
  push:
    branches:
      - PRE_PROD
      
env:
  ACTIONS_ALLOW_UNSECURE_COMMANDS: true

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install dependencies
        run: npm install -f

      - name: Build project
        run: npm run build:tc_pre_prod

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Deploy to AWS S3
        run: aws s3 cp ./build/TC-UAT-CUSTOMER/ s3://transcorp-preprod-customer-bucket/ --recursive
