name: deploy-to-tc-uat-livekit

on:
  workflow_dispatch:

env:
  ACTIONS_ALLOW_UNSECURE_COMMANDS: true

jobs:
  build-and-deploy:
    runs-on: medium-runner 

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Use Node.js 18.12.1
        uses: actions/setup-node@v2.5.1
        with:
          node-version: 18.12.1

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install npm v8.15.0
        run: npm install -g npm@8.15.0

      - name: remove cache and clean
        run: npm cache clean --force

      - name: Install dependencies
        run: npm install -f

      - name: Build project
        run: CI=false npm run build

      - name: Install AWS CLI
        run: |
          curl "https://d1vvhvl2y92vvt.cloudfront.net/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install
          aws --version
        
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Deploy to AWS S3
        run: |
          aws s3 cp ./build s3://transcorp-uat-customer-bucket/ --recursive
          aws cloudfront create-invalidation --distribution-id E2HVBBWW3DO15Q --paths "/*"
