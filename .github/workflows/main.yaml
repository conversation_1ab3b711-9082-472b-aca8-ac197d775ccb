name: Build and Push Docker Image

on:
  workflow_dispatch:
    inputs:
      devsecops_security_gate:
        description: 'DevSecOps Security Gate'
        required: false

jobs:
  CI-Scan:        
      name: CI Pipeline Scan
      uses: m2pfintech/actions/.github/workflows/ci-pipeline-scan.yaml@master
      secrets: inherit
      with:
        product_name: vkyc
        devsecops_security_gate: ${{ inputs.devsecops_security_gate }}
        
  CI-Build:
    name: CI Build
    runs-on: large-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Use Node.js 18.12.1
        uses: actions/setup-node@v2.5.1
        with:
          node-version: 18.12.1

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install npm v8.15.0
        run: npm install -g npm@8.15.0

      - name: remove cache and clean
        run: npm cache clean --force

      - name: Install dependencies
        run: npm i -f

      - name: Build project
        run: CI=false npm run build

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
        
      - name: Upload Build Artifacts
        uses: actions/cache/save@v4
        with:
          path: ./
          key: cache-${{ github.run_id }}

  push_to_dockerhub:
    name: Docker Build & Push
    uses: m2pfintech/github-actions-common/.github/workflows/docker-build-push.yaml@master
    secrets: inherit
    needs: [ CI-Scan, CI-Build ]
    with:
      runner: medium-runner
      product_name: vkyc
      build_docker: "true"
      image_name: "vkyc-v2-customer-frontend"
      use_standard_docker: "false"
      dockerfile: "./Dockerfile"
      cache_path: ./
      devsecops_security_gate: ${{ inputs.devsecops_security_gate }}
