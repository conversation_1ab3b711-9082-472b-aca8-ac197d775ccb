on: 
  push:
    branches:
      - LIVEKIT-UAT
  # pull_request:
  #     types: [opened, synchronize, reopened]

name: SonarQube Scan
jobs:
  sonarqube:
    name: SonarQube Trigger
    runs-on: small-runner
    steps:
    - name: Checking out
      uses: actions/checkout@master
      with:
        # Disabling shallow clone is recommended for improving relevancy of reporting
        fetch-depth: 0
    - name: SonarQube Scan
      uses: kitabisa/sonarqube-action@v1.2.0
      with:
        host: "https://sonarqube.m2pfintech.com"
        login: ${{ secrets.M2P_SONARQUBE_TOKEN }}
        projectBaseDir: "."
        projectKey: "vkyc-v2-customer-frontend"
