name: deploy-to-adani-PROD

on:
  workflow_dispatch:
  
env:
  ACTIONS_ALLOW_UNSECURE_COMMANDS: true

jobs:
  build-and-deploy:
    runs-on: ci

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install Node.js and npm
        run: |
          curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
          sudo apt-get install -y nodejs
          node --version
          npm --version

      - name: Install AWS CLI
        run: |
          curl "https://d1vvhvl2y92vvt.cloudfront.net/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install
          aws --version

      - name: Install dependencies
        run: npm install -f

      - name: Build project
        run: CI=false npm run build
        
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Deploy to AWS S3
        run: aws s3 cp ./build s3://prod-adani-customer-bucket/ --recursive
