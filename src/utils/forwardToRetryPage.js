import { useEffect } from 'react';
import RouteNames from '../Constants/RouteNames';

const forwardToRetryPage = (navigate) => {
    useEffect(() => {
        const handlePopState = () => {
            navigate(RouteNames.KYC_PROCESS_COMPLETED);
        };

        const handleVisibilityChange = () => {
            if (document.visibilityState === 'hidden') {
                sessionStorage.setItem('shouldRedirectKyc', 'true');
            }
        };

        window.addEventListener('popstate', handlePopState);
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // On mount, check if we should redirect (e.g., after reload)
        if (sessionStorage.getItem('shouldRedirectKyc') === 'true') {
            sessionStorage.removeItem('shouldRedirectKyc');
            navigate(RouteNames.KYC_PROCESS_COMPLETED);
        }

        return () => {
            window.removeEventListener('popstate', handlePopState);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [navigate]);
};

export default forwardToRetryPage;