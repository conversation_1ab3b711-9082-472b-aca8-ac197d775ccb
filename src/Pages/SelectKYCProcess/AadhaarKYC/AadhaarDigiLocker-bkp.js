import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import AadhaarDigiLockerCmp from '../../../Components/SelectKYCProcessCmp/AadhaarDigiLockerCmp';
// import AadhaarKYCCmp from '../../../Components/SelectKYCProcessCmp/AadhaarKYCCmp';
import RouteNames from '../../../Constants/RouteNames';
import { actionAadharDigiLocker, actionAadharDigiLockerStatus } from '../../../Store/SagaActions/AadharSagaActions';
import AppFooter from '../../Common/AppFooter';
import { Text } from '../../Language/Language';
// import AppFooter from '../../Common/AppFooter';

const AadhaarDigiLocker = () => {

    let intervalId;
    let windowOpen;
    let windowopenStatus;
    const [digiLockerObj, setdigiLockerObj] = useState({});
    const [aadharDetails, setAadharDetails] = useState({});
    const [isAadharSuccess, setIsAadharSuccess] = useState(false);
    const [closeWindowState, setcloseWindowState] = useState("");
    // console.log(aadharDetails , "Dta")

    let navigate = useNavigate();
    const dispatch = useDispatch();
    const isSafari = /constructor/i.test(window.HTMLElement)
        || (function (p) {
            return p.toString() === "[object SafariRemoteNotification]";
        })(!window['safari'] || (typeof safari !== 'undefined' && window['safari'].pushNotification));
    var userAgent = window.navigator.userAgent;

    const isSafari2 = userAgent.indexOf("Safari") != -1;
    const isSafari3 = userAgent.match(/iPad/i) || userAgent.match(/iPhone/i);

    useEffect(() => {

        const vcipkey = sessionStorage.getItem('vcipkey');
        const model = {
            vcipkey: vcipkey,
            doctype: 'AADHAAR',
            rrn: '1'
        }
        dispatch(actionAadharDigiLocker({ model: model, callback: getDigiLockerData }));
        return () => {
            if (intervalId) {
                clearInterval(intervalId)
            }
            // if (windowopenStatus) windowOpen.close();
            if (closeWindowState) closeWindowState.close();
        };
    }, []);

    const getDigiLockerData = (data) => {
        // windowOpen = window.open(data?.url, "_blank", "toolbar=yes,scrollbars=yes,resizable=yes");
        var closeWindow;
        // if (!isSafari || isSafari2 || !isSafari3) {
        closeWindow = window.open(data?.url, "", "");
        if (!closeWindow || closeWindow.closed || typeof closeWindow.closed == "undefined") {
            alert("Please allow Popup")
        }
        // }
        setcloseWindowState(closeWindow)
        windowopenStatus = true;
        setdigiLockerObj(data);
        const vcipkey = sessionStorage.getItem('vcipkey');
        const model = {
            vcipkey: vcipkey,
            txnid: data.txnid,
            // txnid: "235",
            rrn: "1"
        }
        intervalId = setInterval(() => {
            dispatch(actionAadharDigiLockerStatus({ model: model, callback: getDigiLockerStatusData }));
        }, 5000);
    }

    const getDigiLockerStatusData = (data) => {
        if (!closeWindowState || closeWindowState.closed || typeof closeWindowState.closed == 'undefined' && isAadharSuccess == "false") {
            navigate(RouteNames.SELECT_KYC_PROCESS)
            clearInterval(intervalId);
        }
        if (data?.respcode === "200" && data?.status == "Success") {
            setIsAadharSuccess(true);
            setAadharDetails(data.kycdetails);
            clearInterval(intervalId);
            // if (windowopenStatus) windowOpen.close()
            if (closeWindowState) closeWindowState.close()
        } else if (data?.respcode == "200" && data?.status == "Failed") {
            navigate(RouteNames.SELECT_KYC_PROCESS)
            // windowOpen.close();
            closeWindowState.close();
            clearInterval(intervalId);
        }
        // setInterval(() => {
        //     navigate(RouteNames.PAN_KYC_PROCESS)
        // }, 1000);
    }

    const nextPage = () => {
        // setIsOpen(false);
        navigate(RouteNames.PAN_CAPTURE)
    }


    return (
        <>
            <AadhaarDigiLockerCmp
                aadharDetails={aadharDetails}
                isAadharSuccess={isAadharSuccess}
            />
            {/* <AadhaarKYCCmp /> */}
            {isAadharSuccess && <AppFooter
                btnName={<Text tid="continue_button"/>}
                navigate={nextPage}
            />}
        </>
    )
}

export default AadhaarDigiLocker;