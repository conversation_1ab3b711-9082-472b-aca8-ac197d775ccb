import React from "react";
import { useEffect, useRef, useState } from "react";
import Camera, { FACING_MODES } from "react-html5-camera-photo";
import { useNavigate } from "react-router-dom";
import RouteNames from "../../../Constants/RouteNames";
import "react-html5-camera-photo/build/css/index.css";
import Header from "../../../Layout/Header";
import P from "../../../Components/Elements/P";
import CleintNames from "../../../Constants/ClientNames";
import { useSelector } from "react-redux";
import { Text } from "../../Language/Language";

const PanUpload = () => {
  const navigate = useNavigate();
  window.onpopstate = () => {
    navigate(null);
  }
  const goback = () => {
    // alert("go oback")
    navigate(-1);
  };

  const retakePan = () => {
    // alert("sf")
  };
  const clientName = useSelector((state) => state.HomeReducer.clientName);
  // const isClientTideAccess =
  //   clientName === CleintNames?.TIDEDEV ||
  //     clientName === CleintNames?.TIDEQA ||
  //     clientName === CleintNames?.TIDE
  //     ? true
  //     : false;

  // const isCashbook = clientName === CleintNames?.CASHBOOK ? true : false
  const iskinabank = clientName === CleintNames?.KINABANK ? true : false;


  const handleTakePhoto = (dataUri) => {
    const base64result = dataUri.split(",")[1];
    // setImgPath(base64result);
    // setBase64URL(true);
    if(iskinabank){
      navigate(RouteNames.KINA_OVD_PROCESS, {
        state: { capturedImage: base64result },
      });
    }else{
      navigate(RouteNames.PAN_KYC_PROCESS, {
        state: { capturedImage: base64result },
      });
    }
    
  };

  return (
    <>
      <Header title={""} navigate={() => navigate(-1)} hideHeader={true} />
      <div className="pan_bg">
        <div className="">
          <Camera
            isImageMirror={false}
            onTakePhoto={(dataUri) => {
              handleTakePhoto(dataUri);
            }}
            idealFacingMode={FACING_MODES.ENVIRONMENT}
          />
        </div>
        <div className="VerificationTextStyled">
          {/* <p>Capture PAN Card</p> */}
          <P className="txt text-center" txt={<Text tid="capture_pan_card"/>} />
        </div>
        <div className="VerificationSmallTextStyled">
          {/* <p>Position the front of the PAN card in the frame</p> */}
          <P
            className="txt text-center"
            txt={<Text tid="position_the_front_of_the_PAN_card_in_the_frame"/>}
          />
        </div>
      </div>
      <div className="camera-flex">
        <div className="camera-div">
          <div className="camera-content">
            <p onClick={goback}><Text tid="cancel"/></p>
            <p className="text-muted"> <Text tid="retake"/></p>
            {/* <p onClick={retakePan}> Re-take</p> */}
          </div>
        </div>
      </div>
    </>
  );
};

export default PanUpload;
