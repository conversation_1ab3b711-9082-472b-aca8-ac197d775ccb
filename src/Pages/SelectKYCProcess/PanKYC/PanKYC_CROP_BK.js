import React, { useEffect, useRef, useState } from "react";
import Camera from "react-html5-camera-photo";
import "react-html5-camera-photo/build/css/index.css";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import base64 from "base-64";
import ReactCrop, {
  centerCrop,
  makeAspectCrop,
  Crop,
  PixelCrop,
  onComplete
} from 'react-image-crop';
// import AngleIcon from './images/R.R.svg'

import RouteNames from "../../../Constants/RouteNames";
import {
  actionPanCapture,
  actionSavePanDetails,
  actionVerifyPanNumber,
} from "../../../Store/SagaActions/PanSagaActions";
import AppFooter from "../../Common/AppFooter";
import PanDetailsEdit from "./PanDetailsEdit";
import toast from "react-hot-toast";
import { Text } from "../../Language/Language";
import PanCapture from "../PanUpload/PanCapture";
import Button from "../../../Components/Elements/Button";
import CleintNames from "../../../Constants/ClientNames";
import Tideheader from "../../../Components/TideComponents/Tideheaderforpan/Tideheader";

import 'react-image-crop/dist/ReactCrop.css'
import { useDebounceEffect } from "./useDebounceEffect";
import { canvasPreview } from "./canvasPreview";





const PanKYC = () => {
  var imagebase64 = "";

  const [isCaptureDisabled, setisCaptureDisabled] = useState(false);
  const [panDetails, setPanDetails] = useState({});
  const [dateDisplay, setDateDisplay] = useState("");
  const [vcipkey, setVcipkey] = useState("");

  const [imgPath, setImgPath] = useState("");
  const [uploadimgpath, setUploadimagepath] = useState("");
  const [base64URL, setBase64URL] = useState(false);
  const [fileimg, setFileimg] = useState(null);


  // PAN CROP State!!!!

  const [imgSrc, setImgSrc] = useState('')
  const previewCanvasRef = useRef(null)
  const imgRef = useRef(null)
  const [crop, setCrop] = useState({unit: '%', x: 0, y: 0, width: 100, height: 100})
  const [completedCrop, setCompletedCrop] = useState({unit: 'px', x: 0, y: 0, width: 332.88543701171875, height: 250})
  const [scale, setScale] = useState(1)
  const [rotate, setRotate] = useState(0)
  const [aspect, setAspect] = useState(16 / 9)
  const [cropImage,setCropImage]=useState("")
  const [rotateAngle,setRotateAngle]=useState(0);
  const [errorState,setErrorState]=useState(false)
  console.log(errorState)

  // console.log(crop)

  // PAN CROP STate END !!!


  // Face Detection State Starts !!!

  // Face Detection State Starts !!!


  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { state } = useLocation();

  const isLoading = useSelector((state) => state.HomeReducer.apiStatus);
  const cssProperties = useSelector((state) => state.HomeReducer.cssProperties);
  const clientName = useSelector((state) => state.HomeReducer.clientName);

  const isClientAccess =
    clientName === CleintNames?.TIDEDEV ||
    clientName === CleintNames?.TIDEQA ||
    clientName === CleintNames?.TIDE
      ? true
      : false;
      // const iskinabank = clientName === CleintNames?.KINABANK ? true : false;
  useEffect(() => {
    const { capturedImage, uploadimg } = state;
    if (capturedImage) {
      // console.log("capturedImage", capturedImage);
      setImgPath(capturedImage);
      setBase64URL(true);
    } else if (uploadimg) {
      // navigate(RouteNames.PAN_CAPTURE);
      setUploadimagepath(uploadimg);
      setBase64URL(true);
    }
    const vcipkeyData = sessionStorage.getItem("vcipkey");
    setVcipkey(vcipkeyData);
  }, []);
  // pan UI start
  // const [base64, setBase64] = useState("")

  const videoRef = useRef(null);
  const photoRef = useRef(null);

  const goback = () => {
    navigator.mediaDevices.getUserMedia().then((stream) => {
      const video = videoRef.current;
      video.srcObject = stream;
      video.pause();
    });
    navigate(RouteNames.CHOOSE_LANGAUGE);
  };

  const retakePan = () => {
    setImgPath("");
    setisCaptureDisabled(false);
  };

  const submitCapturedPAN = async() => {
    // if (!completedCrop || !previewCanvasRef.current) {
    //   return;
    // }

  
    previewCanvasRef.current.toBlob(
      async (blob) => {

        const data = await blobToBase64(blob);
         let imgString = data.split(",")[1];
         setCropImage(imgString)
        },
        );

    
  };

  const getPanCapturedData = (data) => {
    // setImgPath("");
    // console.log(data,"-------->")
    
    if(data.respcode=="304"){
      setErrorState(true)
      return
    }

    setPanDetails(data);
    if (!isClientTideAccess) {
      setisCaptureDisabled(true);
    }
    dateFormat(data?.pandetails?.dob);
    if (data.respcode == "200" && isClientTideAccess && data?.pandetails?.pannumber && vcipkey) {
      const panEncode = base64.encode(data?.pandetails?.pannumber);
      const model2 = {
        vcipkey: vcipkey,
        pannumber: panEncode,
        rrn: "1",
      };
      dispatch(
        actionVerifyPanNumber({
          model: model2,
          callback: tidepanverify,
        })
      );
    }else{
      toast.error(<Text tid="OCR_extraction_failed_please_try_again"/>)
    }

  };
  const tidepanverify = (data) => {
    if (data.respcode == "200") {
      navigate(RouteNames.SELECT_VIDEOCALL_LANGAUAGE);
    } else {
      setErrorState(true)
      toast(data?.respdesc);
    }
  };

  const dateFormat = (val) => {
    var newdate = val.split("/").reverse().join("-");
    setDateDisplay(newdate);
  };

  const retake = () => {
    setCropImage("")
    setErrorState(false)
    if (isClientAccess) {
      navigate(RouteNames.PAN_CAPTURE, { replace: true });
    } else {
      if (imgPath || uploadimgpath) {
        if (imgPath) {
          if(iskinabank){
            navigate(RouteNames.KINA_OVD_CAPTURE, { replace: true });
          }else{
            navigate(RouteNames.PAN_CAPTURE, { replace: true });
          }
        } else {
          navigate(RouteNames.SELECT_PAN_KYC_PROCESS, { replace: true });
        }

        // navigate(RouteNames.SELECT_PAN_KYC_PROCESS, { replace: true });
      }
    }
    // else if(uploadimgpath){
    //   navigate(RouteNames.SELECT_PAN_KYC_PROCESS, { replace: true });
    // }
    // setImgPath("");
    // setisCaptureDisabled(false);
    // getVideo();
  };

  const saveKinaIDDetails = (edtname,edtfname,edtdob, edtpannumber, edtgender, edtdateofexpiry, edtcountry, ref3,  ref4,ref5, ref6, ref7, ref8)=>{
    const model = {
      vcipkey: vcipkey,
      edtname:edtname,
      edtfname:edtfname,
      edtdob:edtdob,
      edtpannumber:edtpannumber,
      edtgender:edtgender,
      ref2:edtdateofexpiry,
      ref1:edtcountry,
      ref3:ref3,
      ref4:ref4,
      ref5:ref5,
      ref6:ref6,
      ref7:ref7,
      ref8:ref8
    }
    dispatch(actionSavePanDetails({ model: model, callback: getSaveDetailsRes }));
  }


  const savePanDetails = (
    edtname,
    edtfname,
    edtdob,
    edtpannumber,
  ) => {
    const panEncode = base64.encode(
      edtpannumber ? edtpannumber : panDetails?.pandetails?.pannumber
    );
    const model2 = {
      vcipkey: vcipkey,
      pannumber: panEncode,
      rrn: "1",
    };
    dispatch(
      actionVerifyPanNumber({
        model: model2,
        callback: (data) =>
          getVerifyPanData(
            data,
            edtname,
            edtfname,
            edtdob,
            edtpannumber,
          ),
      })
    );
  };

  const getVerifyPanData = (
    data,
    edtname,
    edtfname,
    edtdob,
    edtpannumber,
    edtgender
  ) => {
    if (edtname || edtfname || edtdob || edtpannumber || edtgender) {
      const model = {
        vcipkey: vcipkey,
        edtname: edtname,
        edtfname: edtfname,
        edtdob: edtdob,
        edtpannumber: edtpannumber,
      };
      dispatch(
        actionSavePanDetails({ model: model, callback: getSaveDetailsRes })
      );
    } else {
      navigate(RouteNames.SELECT_VIDEOCALL_LANGAUAGE);
    }
  };

  const getSaveDetailsRes = (data) => {
    navigate(RouteNames.SELECT_VIDEOCALL_LANGAUAGE);
  };

  // const panCapture_pic = () => {
  //   navigate(RouteNames.PAN_CAPTURE)
  // }

  const handleTakePhoto = (dataUri) => {
    const base64result = dataUri.split(",")[1];
    setImgPath(base64result);
    setBase64URL(true);
  };

  const isClientTideAccess =
    clientName === CleintNames?.TIDEDEV ||
    clientName === CleintNames?.TIDEQA ||
    clientName === CleintNames?.TIDE
      ? true
      : false;
  const iskinabank = clientName === CleintNames?.KINABANK ? true : false;

  // PAN Crop function Starts Here!!!!

  function centerAspectCrop(
    mediaWidth, mediaHeight, aspect
  ) {
    return centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        aspect,
        mediaWidth,
        mediaHeight,
      ),
      mediaWidth,
      mediaHeight,
    )
  }

  function onImageLoad(e) {
    // console.log(e)
    if (aspect) {
      const { width, height } = e.currentTarget
      setCrop(centerAspectCrop(width, height, aspect))
    }
  }

  useDebounceEffect(
    async () => {
      if (
        completedCrop?.width &&
        completedCrop?.height &&
        imgRef.current &&
        previewCanvasRef.current
      ) {
        // We use canvasPreview as it's much faster than imgPreview.
        canvasPreview(
          imgRef.current,
          previewCanvasRef.current,
          completedCrop,
          scale,
          rotate,
        )
      }
    },
    100,
    [completedCrop, scale, rotate],
  )

  function handleToggleAspectClick() {
    if (aspect) {
      setAspect(undefined)
    } else if (imgRef.current) {
      const { width, height } = imgRef.current
      setAspect(16 / 9)
      setCrop(centerAspectCrop(width, height, 16 / 9))
    }
  }
  useEffect(()=>{
    handleToggleAspectClick()
  },[])


  const blobToBase64 = (blob) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    return new Promise(resolve => {
      reader.onloadend = () => {
        resolve(reader.result);
      };
    });
  };


  const handelChange=(e)=>{
    // console.log(e.target.value)
    setRotateAngle(e.target.value)
    setRotate(e.target.value)
  }

  useEffect(async()=>{
    if(cropImage!=""){


      const model = {
        vcipkey: vcipkey,
        panimage: cropImage,
        pwd: "",
        rrn: "1",
      };
     dispatch(actionPanCapture({ model: model, callback: getPanCapturedData }));
    }

  },[cropImage])
  
  // PAN CROP Function End here


  // Face Detection Function Starts



  // Face Detection Function Ends




  return (
    <>
      {(imgPath || uploadimgpath) && !isCaptureDisabled ? (
        <>
          <div
            style={{
              height: "calc(100vh - 55px)",
              position: "relative",
              zIndex: 99,
            }}
          >
            {isClientTideAccess ? (
              <Tideheader
                noarrow={"noarrow"}
                text={<Text tid="confirm_id"/>}
                navigate={() => {
                  navigate(-1);
                }}
              />
            ) : (
              <div className="pandisplaytext">{iskinabank ? "Confirm ID":"Image Crop"}</div>
            )}
            <div className="display-img">
                <div className="cropPageSubHead" style={{fontWeight:'500',fontSize:'1rem',paddingBottom:'01rem'}}>
                <Text tid="please_crop_the_image_of_PAN_card"/>
                </div>
                <p className="PanCardImageText" style={{fontWeight: '400',fontSize:' 14px',color: '#121212',opacity:' 0.6',marginBottom:'0.7rem'}}>
                <Text tid="pan_card_image"/>
                </p>
              <div className=" text-center capture-photo">


              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={aspect}
                style={{borderRadius:'0.5rem'}}
                >
                {base64URL && imgPath ? (
                  <img
                    src={"data:image/png;base64," + imgPath}
                    className="panimg"
                    alt="PAN Image"
                    style={{ transform: `scale(${scale}) rotate(${rotate}deg)` }}
                    onLoad={onImageLoad}
                    ref={imgRef}
                  />
                ) : (
                  <img
                    src={"data:image/png;base64," + uploadimgpath}
                    className="panimg"
                    alt="PAN Image"
                    style={{ transform: `scale(${scale}) rotate(${rotate}deg)` }}
                    onLoad={onImageLoad}
                    ref={imgRef}

                  />
                )}
              </ReactCrop>

              </div>
              <div className="slidecontainer">
              <div className="SliderHederTextCropPage">
                <div><Text tid="rotate_image"/></div>
                <div>{rotateAngle}<sup>0</sup></div>
              </div>

              <div style={{display:'flex',gap:'1rem',alignItems:'center',padding:'0.8rem 0'}}>
              <input type="range" min="-180" max="180" value={rotateAngle} className="slider" id="myRange" onChange={handelChange}/>
              <img src="../images/R.R.svg"  />
              </div>
              </div>
              {/* {isClientTideAccess ? (
                <div style={{ marginTop: "20px",fontSize:"10px" }}>
                  Please ensure you upload an image of your physical PAN card.
                  ePAN images are not accepted
                </div>
              ) : null} */}
            </div>


            <div style={{'display':'none'}}>
             {!!completedCrop && (
              <canvas
            ref={previewCanvasRef}
            style={{
              // border: '1px solid black',
              objectFit: 'contain',
              width: completedCrop.width,
              height: completedCrop.height,
              display:'block',
              margin:'0 auto',
              minHeight: "13rem",
              padding: "12px"            }}
          />
             )}
      
      </div>


            {/* <button className='btn w-100' onClick={submitCapturedPAN}>Submit</button> */}
            <div
              className={
                "panverifybutton pan-bx-btns text-center mt-3 display-btn"
              }
              style={{
                backgroundColor: isClientTideAccess
                  ? "rgb(241, 243, 244)"
                  : "FFFFFFF2",
              }}
            >
              <Button
                className="btn w-auto mx-2"
                type="button"
                disabled={isLoading || errorState}
                click={submitCapturedPAN}
                title={
                  isClientTideAccess ? <Text tid="looks_good"/> : <Text tid="submit" />
                }
                isClientTideAccess={isClientTideAccess}
                color={cssProperties?.button?.text_color}
                fontSize={cssProperties?.button?.font_size}
                backgroundColor={cssProperties?.button?.color}
              />
              <Button
                className={
                  isClientTideAccess
                    ? "tidepanbtn w-auto retake mx-2"
                    : "panbtn w-auto retake mx-2"
                }
                border={cssProperties?.button?.color}
                type="button"
                disabled={isLoading}
                color={cssProperties?.button?.color}
                click={retake}
                title={
                  isClientTideAccess ? (
                    <Text tid="try_again"/>
                  ) : base64URL && imgPath ? (
                    <Text tid="retake" />
                  ) : (
                    <Text tid="re_upload"/>
                  )
                }
              />
              {/* <button className='btn w-auto retake mx-2' disabled={isLoading} onClick={retake}>
              <Text tid="retake" />
            </button>
            <button className='btn w-auto mx-2' disabled={isLoading} onClick={submitCapturedPAN}>
              <Text tid="submit" />
            </button> */}
            </div>
          </div>
        </>
      ) : null}

      {isCaptureDisabled && (
        <PanDetailsEdit
          panDetails={panDetails?.pandetails}
          dateDisplay={dateDisplay}
          savePanDetails={savePanDetails}
          saveKinaIDDetails={saveKinaIDDetails}
          navigate={navigate}
        />
      )}

      {/* {imgPath && <AppFooter
        btnName="Submit"
        navigate={submitCapturedPAN}
      />} */}

    </>
  );
};

export default PanKYC;
