import React, { useEffect, useState } from "react";
import { Text } from "../Language/Language";
var verhoeff = require("node-verhoeff");

const AadhaarNumberModalOkyc = (props) => {
  const [aadharNumber, setAadharNumber] = useState("");
  const [aadharErr, setAadharErr] = useState("");
  const [captcha<PERSON>ey, setCaptchaKey] = useState("");
  const [VIDNumber, setVIDNumber] = useState("");
  const [isAadharVID, setisAadharVID] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "aadharNumber" && value.length <= 12) {
      //   var regexp = /^[2-9]{1}[0-9]{3}\s{1}[0-9]{4}\s{1}[0-9]{4}$/;
      var regexp = /^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$/;
      let err = "";
      if (regexp.test(value)) {
        err = "";
        // if (value?.length === 12) {
        //   const isValid = verhoeff.validateAadhaar(value);
        //   if (isValid) {
        //     err = "";
            // props.sendOTPModal(aadharNumber);
        //   } else {
        //     err = "Invalid Aadhar no.";
        //   }
        // }
      } else {
        err = "Invalid Aadhar no.";
      }
      setAadharErr(err);

      const result = value.replace(/\D/g, '');
      setAadharNumber(result);
    }
    if (name === "captchaKey") {
      setCaptchaKey(value);
    }
  };

  const submitAadhar = () => {
    props.sendOTPModal(aadharNumber);
    // props.getOTPModal(captchaKey);
    // if (props?.captchaDetails?.captcha && captchaKey && !props.aadharapi) {
    //   props.getOTPModal(captchaKey);
    // } else {
    //   if (props.aadharapi) {
    //     const isValid = verhoeff.validateAadhaar(aadharNumber);
    //     if (isValid) {
    //       
    //     } else {
    //       setAadharErr("Invalid Aadhar no.");
    //     }
    //   }
    // }
  };

  const switchAadharVID = () => {
    setisAadharVID(prev => !prev)
  }




  return (
    <>
      <div className="modal-header align-items-center">
        <h5
          className="modal-title text-center"
          id="exampleModalLabel"
          style={{ visibility: "hidden" }}
        >
          {/* Aadhaar */}
        </h5>
        <button
          type="button"
          className="close1"
          data-dismiss="modal"
          aria-label="Close"
          onClick={props.closeModal}
        >
          <img src="../images/icon-close.svg" alt="close" />
        </button>
      </div>
      <div className="modal-body">
        <h4 className="title text-center">{isAadharVID === false ? <Text tid="aadhaar"/> : <Text tid="VID"/>} <Text tid="number"/></h4>
        <p className="txt text-center">
          <Text tid="enter_your"/> {isAadharVID == false ? <Text tid="twelve_digit_aadhaar"/> : <Text tid="sixteen_digin_aadhar"/>} <Text tid="number_to_begin" />
        </p>
        <div className="app-body-frm">
          <div className="frm-grp">
            {props?.isCaptchaTrue ? (
              <input
                type="number"
                name="aadharNumber"
                value={aadharNumber}
                className="frm-grp-inp"
                placeholder="Aadhaar Number*"
                disabled
                required
                style={{ opacity: 0.5 }}
              />
            ) : (
              <>
                {isAadharVID === false ? <input
                  type="number"
                  name="aadharNumber"
                  value={aadharNumber}
                  onChange={handleChange}
                  className="frm-grp-inp"
                  placeholder="Aadhaar Number*"
                  required
                /> :
                  <input
                    type="number"
                    name="VIDNumber"
                    value={VIDNumber}
                    onChange={handleChange}
                    className="frm-grp-inp"
                    placeholder="VID Number*"
                    required
                  />
                }
                {/* <a
                  className=""
                  onClick={switchAadharVID}
                >
                  Switch to {!isAadharVID ? "VID" : "Aadhar"}
                </a> */}
              </>
            )}
            {aadharErr && (
              <p className="error position-absolute text-danger m-0 small">
                {aadharErr}
              </p>
            )}


          </div>


          {props.isCaptchaTrue ? <div className="frm-grp "
            style={{
              display: 'flex', justifyContent: "flex-start",
              alignItems: 'center',
              opacity: props?.isCaptchaTrue ? 1 : 0.5
            }}
          >
            <div
              style={{ marginRight: "1rem" }}
            >
              <img
                src={
                  props?.isCaptchaTrue
                    ? "data:image/png;base64," + props?.captchaDetails?.captcha
                    : "../images/captcha.jpeg"
                }
                className="border"
                alt="close"
              />
            </div>
            <div
              onClick={props.sendRefreshOTPModal}

            >
              <i class="fa-sharp fa-solid fa-arrows-rotate"></i> <Text tid="refresh" />
            </div>
          </div> : null}



          <div className="frm-grp">
            {props?.isCaptchaTrue ? (
              <input
                type="text"
                name="captchaKey"
                onChange={handleChange}
                className="frm-grp-inp"
                placeholder="Enter Security Code*"
              />
            ) : null}
            {/*
              <input
                type="number"
                //   name
                className="frm-grp-inp"
                placeholder="Enter Security Code*"
                disabled
                readOnly
              />*/}
          </div>
          {props?.isCaptchaTrue && (
            <p className="txt text-left">
              <Text tid="type_the_character_you_see_in_the_picture"/>
            </p>
          )}
        </div>
      </div>
      <div className="modal-footer d-flex">
        {/* <button type="button" className="btn w-auto btn-white" data-dismiss="modal">Close</button> */}

        {props.aadharapi ?
          <button
            type="submit"
            className="btn w-auto"
            disabled={aadharErr || aadharNumber.length < 12}
            onClick={submitAadhar}
          >
            <Text tid="get_otp"/>
          </button>
          :
          <button
            type="submit"
            className="btn w-auto"
            disabled={captchaKey.length < 5}
            onClick={submitAadhar}
          >
            <Text tid="submit_otp"/>
          </button>
        }
      </div>
    </>
  );
};

export default AadhaarNumberModalOkyc;
