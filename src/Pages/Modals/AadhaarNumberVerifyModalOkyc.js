import React, { useEffect, useRef, useState } from "react";
import { Text } from "../Language/Language";

const AadhaarNumberVerifyModalOkyc = (props) => {
  const [verificationCode, setVerificationCode] = useState({});
  const [shareCode, setShareCode] = useState({});

  const inputRef = useRef({});
  const inputRefSC = useRef({});

  const handleChangeVC = (e) => {
    const { name, value } = e.target;
    // console.dir(inputRef.current[name]);
    var regexp = /^[0-9]{1}$/;
    if (regexp.test(value) && value?.length === 1) {
      setVerificationCode((prevState) => ({ ...prevState, [name]: value }));
      switch (name) {
        case "vc1":
          inputRef.current["vc2"].focus();
          break;
        case "vc2":
          inputRef.current["vc3"].focus();
          break;
        case "vc3":
          inputRef.current["vc4"].focus();
          break;
        case "vc4":
          inputRef.current["vc5"].focus();
          break;
        case "vc5":
          inputRef.current["vc6"].focus();
          break;

        default:
          break;
      }
    } else if (value?.length === 0) {
      setVerificationCode((prevState) => ({ ...prevState, [name]: value }));
    }
  };


  const handleChangeSC = (e) => {
    const { name, value } = e.target;
    // setShareCode({ [name]: value });
    var regexp = /^[0-9]{1}$/;
    if (regexp.test(value) && value?.length === 1) {
      setShareCode((prevState) => ({ ...prevState, [name]: value }));
      switch (name) {
        case "sc1":
          inputRefSC.current["sc2"].focus();
          break;
        case "sc2":
          inputRefSC.current["sc3"].focus();
          break;
        case "sc3":
          inputRefSC.current["sc4"].focus();
          break;

        default:
          break;
      }
    } else if (value?.length === 0) {
      setShareCode((prevState) => ({ ...prevState, [name]: value }));
    }
  };

  const [disabledbtn, setDisabledbtn] = useState(true)
  let OTP = Object.values(verificationCode).join("")
  let sharecode = Object.values(shareCode).join("")
    
  useEffect(() => {
    (OTP.length < 6) ? setDisabledbtn(true) : setDisabledbtn(false)
  }, [verificationCode, sharecode])


  const verifyOKYC = () => {
    if (OTP.length === 6 ) {
      props.getOKYCdetail(OTP, sharecode)
    }
    else { 
      
    }
  }

  return (
    <>
      <div className="modal-header align-items-center">
        <h5
          className="modal-title text-center"
          id="exampleModalLabel"
          style={{ visibility: "hidden" }}
        >
          
        </h5>
        <button
          type="button"
          className="close1"
          data-dismiss="modal"
          aria-label="Close"
          onClick={props.closeModal}
        >
          <img src="../images/icon-close.svg" alt="close" />
        </button>
      </div>
      <div className="modal-body">
        <div className="text-center mb-3">
          <img src="../images/icon-lock.svg" alt="lock" />
        </div>
        <h4 className="title text-center"><Text tid="verification_code"/></h4>
        <p className="txt text-center">
          <Text tid="Please_enter_the_verification_code_we_sent_to_your_phone_number"/>
        </p>
        <div className="secret-codes">
          <input
            type="number"
            name="vc1"
            value={verificationCode?.vc1}
            onChange={handleChangeVC}
            ref={(el) => (inputRef.current["vc1"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
            autoComplete="off"
          />
          <input
            type="number"
            name="vc2"
            value={verificationCode?.vc2}
            onChange={handleChangeVC}
            ref={(el) => (inputRef.current["vc2"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
            autoComplete="off"
          />
          <input
            type="number"
            name="vc3"
            value={verificationCode?.vc3}
            onChange={handleChangeVC}
            ref={(el) => (inputRef.current["vc3"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
            autoComplete="off"
          />
          <input
            type="number"
            name="vc4"
            value={verificationCode?.vc4}
            onChange={handleChangeVC}
            ref={(el) => (inputRef.current["vc4"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
            autoComplete="off"
          />
          <input
            type="number"
            name="vc5"
            value={verificationCode?.vc5}
            onChange={handleChangeVC}
            ref={(el) => (inputRef.current["vc5"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
            autoComplete="off"
          />
          <input
            type="number"
            name="vc6"
            value={verificationCode?.vc6}
            onChange={handleChangeVC}
            ref={(el) => (inputRef.current["vc6"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
            autoComplete="off"
          />
        </div>
        <div className="">
        </div>
        {/* <h4 className="title text-center">Enter Share Code</h4> */}
        {/* <p className="txt text-center">
          Create a 4 digit code to secure your offline eKYC
        </p> */}
        {/* <div className="secret-codes"> */}
          {/* <input
            type="number"
            name="sc1"
            className="scretet-inp"
            value={shareCode?.sc1}
            onChange={handleChangeSC}
            ref={(el) => (inputRefSC.current["sc1"] = el)}
            maxLength={1}
            pattern="[0-9]+"
            required
          />
          <input
            type="number"
            name="sc2"
            value={shareCode?.sc2}
            onChange={handleChangeSC}
            ref={(el) => (inputRefSC.current["sc2"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
          />
          <input
            type="number"
            name="sc3"
            value={shareCode?.sc3}
            onChange={handleChangeSC}
            ref={(el) => (inputRefSC.current["sc3"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
          />
          <input
            type="number"
            name="sc4"
            value={shareCode?.sc4}
            onChange={handleChangeSC}
            ref={(el) => (inputRefSC.current["sc4"] = el)}
            className="scretet-inp"
            maxLength={1}
            min={1}
          /> */}
          {/* <input type="number" name="" class="scretet-inp" maxlength="1" min="1" />
                    <input type="number" name="" class="scretet-inp" maxlength="1" min="1" /> */}
        {/* </div> */}
      </div>
      <div className="modal-footer d-flex">
        {/* <button type="button" className="btn w-auto btn-white" data-dismiss="modal" onClick={props.closeModal}>Close</button> */}
        <button
          type="button"
          className="btn w-auto"
          proceed
          onClick={() => verifyOKYC()}
          disabled={disabledbtn}
        >
          <Text tid="agree" />
        </button>
      </div>
    </>
  );
};

export default AadhaarNumberVerifyModalOkyc;
