import React from 'react';
import { Text } from '../Language/Language';

const ToggleScreenModal = (props) => {
    return (
        <>
            <div className="modal-header align-items-center">
                <h5 className="modal-title text-center w-100" id="exampleModalLabel">
                    <Text tid="user_consent" />

                    {/* User Consent */}
                </h5>
                {/* <button type="button" className="close" data-dismiss="modal" aria-label="Close" onClick={props.closeModal}>
                    <i className="fa-solid fa-xmark"></i>
                </button> */}
                <button type="button" className="close" data-dismiss="modal" aria-label="Close" onClick={props.closeModal}>
                    <img src="images/icon-close.svg" alt="close" />
                </button>
            </div>
            <div className="modal-body">
                <p className="txt">
                    <Text  tid="toggle_your_video_call_screen_with_agent_screen_for_photo_capture"/>
                </p>

            </div>
            <div className="modal-footer d-flex">
                <button type="button" className="btn w-auto btn-white" data-dismiss="modal" onClick={props.closeModal}>
                    <Text tid="disagree" />
                    {/* Disagree */}
                </button>
                <button type="button" className="btn w-auto" onClick={props?.togvid}>
                    <Text tid="agree" />

                    {/* Agree */}
                </button>
            </div>
        </>
    )
}

export default ToggleScreenModal;