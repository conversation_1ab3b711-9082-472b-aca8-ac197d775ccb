/* video {
    width: 100%;
    height: auto;
    float: left;
    cursor: pointer;
} */
/* .streamcomponent div {
    position: absolute;
    background: #f8f8f8;
    padding-left: 5px;
    padding-right: 5px;
    color: #777777;
    font-weight: bold;
    border-bottom-right-radius: 4px;
}
p{
    margin: 0;
}
#main-video .video-play{
    width: 100vw !important;
    height: 100vh !important;
    object-fit: cover;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 9;
}

#agent{
    width: 100px;
    height: 150px;
    border-radius: 4px;
    border: 1.5px solid #FFFFFF;
    filter: drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.12));
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999;
}
#agent video{
    width: 100px;
    height: 150px;
    object-fit: cover;
    border-radius: 4px;
    border: 1.5px solid #FFFFFF;
    filter: drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.12));
} */

