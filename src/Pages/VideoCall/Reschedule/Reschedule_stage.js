import React, { useEffect, useState } from "react";
import RescheduleCmp from "../../../Components/VideoCallCmp/RescheduleCmp/RescheduleCmp";
import "react-calendar/dist/Calendar.css";
import AppFooter from "../../Common/AppFooter";
import { useDispatch } from "react-redux";
import moment from "moment";
import {
  actionCancelScheduleAction,
  actionGetScheduleCalenderAction,
  actionGetScheduleDetailsAction,
  actionRescheduleSagaAction,
} from "../../../Store/SagaActions/InitiateVideoCallSagaActions";
import { useNavigate } from "react-router-dom";
import RouteNames from "../../../Constants/RouteNames";
import Header from "../../../Layout/Header";
import { Text } from "../../Language/Language";

const Reschedule = (props) => {
  const [value, onChange] = useState(new Date());
  const [sdate, setSdate] = useState("");
  const [sTime, setSTime] = useState("");
  const [scheduleCalenderData, setScheduleCalenderData] = useState({});
  const [scheduleDetails, setScheduleDetails] = useState({});
  const [isRescheduleFormEnabled, setIsRescheduleFormEnabled] = useState(false);
  const [intervalId, setIntervalId] = useState(null);
  const [intervalId2, setIntervalId2] = useState(null);

  // let intervalId;
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const vcipkeyData = sessionStorage.getItem("vcipkey");
  const langidData = sessionStorage.getItem("langid");

  useEffect(() => {
    if (
      props?.InitiateVCFQ?.tokennumber === "-1" ||
      props?.InitiateVCFQ?.isscheduled === "1"
    ) {
      getScheduleDetails();
      let intervalVal = setInterval(() => {
        if (!intervalId || intervalId != intervalVal) {
          setIntervalId(intervalVal);
        }
        getScheduleDetails();
      }, 7000);
    } else {
      getScheduleCalender(new Date());
    }
    return () => {
      clearAPICall();
    };
  }, []);

  useEffect(() => {
    if (scheduleDetails?.joinstatus === "-1") {
      getCancelScheduleDetails();
    }
    // console.log("asdasdas");
  }, [scheduleDetails]);

  const clearAPICall = () => {
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  };

  const getScheduleDetails = () => {
    const model = {
      vcipkey: vcipkeyData,
    };
    dispatch(
      actionGetScheduleDetailsAction({
        model: model,
        callback: getScheduleDetailsData,
      })
    );
  };

  const getScheduleDetailsData = (data) => {
    setScheduleDetails(data);
  };

  const getScheduleCalender = (val) => {
    const model = {
      vcipkey: vcipkeyData,
      langid: langidData,
      sdate: moment(val).format("YYYY-MM-DD"),
    };
    dispatch(
      actionGetScheduleCalenderAction({
        model: model,
        callback: getScheduleCalenderData,
      })
    );
  };

  const getScheduleCalenderData = (data) => {
    setScheduleCalenderData(data);
  };

  const handleChange = (e) => {
    const name = e.target.name;
    const value = e.target.value;
    setSTime(value);
  };

  const rescheduleVC = () => {
    const model = {
      vcipkey: vcipkeyData,
      sdate: moment(value).format("YYYY-MM-DD"),
      stime: sTime,
      langid: langidData,
    };
    dispatch(
      actionRescheduleSagaAction({ model: model, callback: getRescheduleData })
    );
  };

  const getRescheduleData = (data) => {
    if (data.respcode === "200") {
      setSTime("");
      setIsRescheduleFormEnabled(false);
      // props?.setIsRescheduled(false);
      getScheduleDetails();
      let interval2 = setInterval(() => {
        if (!intervalId2 || intervalId2 != interval2) {
          setIntervalId2(interval2);
        }
        getScheduleDetails();
      }, 7000);
      // navigate(RouteNames.INITIATE_VIDEO_CALL)
    }
  };

  const joinCall = () => {
    clearAPICall();
    props?.joinVideoSession();
  };

  const cancelSchedule = () => {
    const model = {
      vcipkey: vcipkeyData,
    };
    dispatch(
      actionCancelScheduleAction({
        model: model,
        callback: getCancelScheduleDetails,
      })
    );
  };

  const getCancelScheduleDetails = (data) => {
    // clearAPICall();
    if (intervalId) {
      clearInterval(intervalId);
    }
    if (intervalId2) {
      clearInterval(intervalId2);
    }
    props?.cancelledSchedule(data);
  };

  const calenderDateSelected = (val) => {
    onChange(val);
    getScheduleCalender(val);
  };

  return (
    <>
      <Header
        title={<Text tid="reschedule" />}
        navigate={() => navigate(-1)}
        isClientTideAccess = {props?.isClientTideAccess}
        hideHeader={false}
      />
      <article className="app-body">
        <RescheduleCmp
          InitiateVCFQ={props?.InitiateVCFQ}
          isRescheduled={props?.isRescheduled}
          isClientTideAccess = {props?.isClientTideAccess}
          value={value}
          scheduleDetails={scheduleDetails}
          isRescheduleFormEnabled={isRescheduleFormEnabled}
          scheduleCalenderData={scheduleCalenderData}
          onChange={calenderDateSelected}
          handleChange={handleChange}
          joinCall={joinCall}
          cancelSchedule={cancelSchedule}
          id={props?.id}
        />
        <div className={"bookslotbuttonposition"} style={{backgroundColor: props?.isClientTideAccess ? "#F1F2F3" : "#FFFFFFF2",
         paddingBottom:props?.isClientTideAccess ? "4%" : "4%"}}>
        {(value && sTime) ||
        (props?.isRescheduled && !scheduleDetails?.sdate) ? (
          <AppFooter
            btnName={"Confirm"}
            isClientTideAccess = {props?.isClientTideAccess}
            isDisabled={value && sTime ? false : true}
            navigate={value && sTime ? rescheduleVC : null}
          />
        ) : null}
        </div>
      </article>
    </>
  );
};

export default Reschedule;
