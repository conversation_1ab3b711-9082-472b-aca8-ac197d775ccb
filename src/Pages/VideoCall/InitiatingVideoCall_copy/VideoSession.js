import React from 'react'
import OpenViduVideoComponent from './OvVideo'

const VideoSession = (props) => {
    // console.log("props->->", props);
    return (
        <>
            {/* {props.subscribers?.map((sub, i) => {
                if (sub.stream.typeOfVideo !== "SCREEN") {
                    return <div key={i} className="stream-container othervideo">
                        <OpenViduVideoComponent streamManager={sub} />
                    </div>
                }
            })} */}

            <div className="myvideo">

                {props?.streamManager !== undefined ? (
                    <div id="main-video" className="streamcomponent">
                        <OpenViduVideoComponent streamManager={props?.streamManager} />
                        {/* <div><p>{this.getNicknameTag()}</p></div> */}
                    </div>
                ) : null}
                {/* {props.mainStreamManager !== undefined ? (
                    <div id="main-video">
                        <OpenViduVideoComponent streamManager={props.mainStreamManager} />
                    </div>
                ) : null} */}
            </div></>
    )
}

export default VideoSession