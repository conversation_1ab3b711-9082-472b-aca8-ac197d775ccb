import { OpenVidu } from 'openvidu-browser';
import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import InitiatingVideoCallCmp from '../../../Components/VideoCallCmp/InitiatingVideoCallCmp'
import RouteNames from '../../../Constants/RouteNames';
import { createVideoSessionSagaAction } from '../../../Store/SagaActions/VideoSagaActions';
import OpenViduVideoComponent from './OvVideo';
// import AppFooter from '../../Common/AppFooter';
import VideoSession from './VideoSession';
import './video.css';
import { actionNoficationListSaga } from '../../../Store/SagaActions/CommonSagaActions';
import { Text } from '../../Language/Language';

const InitiatingVideoCall = () => {
  const [session, setSession] = useState(undefined);
  const [subscribers, setSubscribers] = useState([]);
  const [mainStreamManager, setMainStreamManager] = useState(undefined);
  const [publisher, setPublisher] = useState(undefined);
  // const [notificationIntervalId, setNotificationIntervalId] = useState('');
  const [notificationList, setNotificationList] = useState({});
  const [displayQtn, setDisplayQtn] = useState({});

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const params = useParams();

  let OV;
  var notificationIntervalId;

  useEffect(() => {
    joinVideoSession();
    return () => {
      endVideoCall();
      clearNotificationInterval();
      // leaveCallWithoutEndSession();
    }
  }, []);

  useEffect(() => {
    if (notificationList?.notificationid === '2') {
      endVideoCall();
    }

  }, [notificationList])

  // useEffect(() => {
  //   let cont = 0;
  //   if (session) { 
  //     cont = cont +1;
  //     console.log("cont---", cont);
  //     joinVideoSession();
  //   }
  // }, [session])


  const clearNotificationInterval = () => {
    // console.log("notificationIntervalId-->", notificationIntervalId);
    if (notificationIntervalId) {
      clearInterval(notificationIntervalId)
    }
  }

  const startVideoSession = () => {
    OV = new OpenVidu();
    let sessionData = OV.initSession();
    // console.log("--------------", sessionData);
    setSession(sessionData);
  }

  const joinVideoSession = () => {
    OV = new OpenVidu();
    var mySession = OV.initSession();
    setSession(mySession);
    // var mySession = session;
    mySession.on('streamCreated', (event) => {
      var subscriber = mySession.subscribe(event.stream, undefined);

      if (event.stream.typeOfVideo !== "SCREEN") {
        sessionStorage.setItem("connectionId", event.stream.connection.connectionId);
      }
      var subscriberArr = subscribers;
      subscriberArr.push(subscriber);
      sessionStorage.setItem("subscribers", Object.keys(subscriberArr))
      setSubscribers(subscriberArr);
    });
    mySession.on('streamDestroyed', (event) => {
      deleteSubscriber(event.stream.streamManager);
    });

    const name = params.id;
    const model = {
      name: name,
      sessionId: name,
      OV: OV,
      session: mySession,
      myUserName: name
    }
    dispatch(createVideoSessionSagaAction({ model: model, callback: getCreateSessionData }));
  }

  // DELETE VIDEO CALL SUBSCRIBER
  const deleteSubscriber = (streamManager) => {
    let subscribersData = [...subscribers];
    let index = subscribersData.indexOf(streamManager, 0);
    if (index > -1) {
      subscribersData.splice(index, 1);
      setSubscribers(subscribersData);
      // this.setState({
      //   subscribers: subscribers,
      // });
    }
  }

  const getCreateSessionData = (data) => {
    setMainStreamManager(data.mainStreamManager)
    setPublisher(data.publisher)
    notificationIntervalId = setInterval(() => {
      // if (!notificationIntervalId) {
      // setNotificationIntervalId(nfyId)
      // }
      dispatch(actionNoficationListSaga({ callback: getNoficationData }));
    }, 1000);
  }

  const getNoficationData = (data) => {
    setNotificationList(data);
    // if (data?.notificationid === '1') {
    // } else if (data?.notificationid === '2') {
    // }
  }


  // END VIDEO CALL
  const endVideoCall = () => {
    if (session) {
      session.disconnect();
    }
    sessionStorage.removeItem("session");
    sessionStorage.removeItem("connectionId");
    sessionStorage.removeItem("videoconfsessionid");
    sessionStorage.removeItem("publisher");
    sessionStorage.removeItem("session");
    setTimeout(() => {
      navigate(RouteNames.KYC_PROCESS_COMPLETED, { replace: true });
    }, 1000);
  }

  const nextPage = () => {
    navigate(RouteNames.KYC_PROCESS_COMPLETED);
  }

  // console.log("op->->subscribers", subscribers, "op->->mainStreamManager", mainStreamManager, "op->->session", session);
  console.log("op->->subscribers", subscribers)

  return (
    <>
      {!session
        ? <InitiatingVideoCallCmp />
        : <>
          {subscribers?.length !== 0 ? null
            : <p className="text-center text-danger">
              <Text tid="waiting_agent"/>
            </p>
          }
          {subscribers?.map((sub, i) => {
            if (sub.stream.typeOfVideo !== "SCREEN") {
              return <div key={i} className="stream-container othervideo">
                {/* <VideoSession streamManager={sub} /> */}
              </div>
            }
          })}
          {mainStreamManager ? (
            <div id="agent">
              <OpenViduVideoComponent streamManager={mainStreamManager} />
            </div>
          ) : null}
          {/* <div className="myvideo h-100 position-relative">
            {mainStreamManager ? (
              <div id="main-video">
                <OpenViduVideoComponent streamManager={mainStreamManager} />
              </div>
            ) : null}
          </div> */}
          {notificationList && Object.values(notificationList)?.length > 0
            ? (notificationList?.notificationid === '1' && notificationList?.notifications ? <div className='display-qtn'>
              <span>{notificationList?.notifications}</span>
            </div> : (null))
            : (null)}

          {/* <VideoSession subscribers={subscribers} /> */}
        </>
      }
    </>
  )
}

export default InitiatingVideoCall;