import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { detect } from "detect-browser";
import TokenNumberCmp from "../../../Components/VideoCallCmp/TokenNumberCmp/TokenNumberCmp";
import RouteNames from "../../../Constants/RouteNames";
import Micvideocheckmodel from "../../Modals/Micvideocheckmodel";
import Button from "../../../Components/Elements/Button";
import {
  actionGetInitiateConferenceQueueSagaAction,
  actionGetUpdatedTokenSagaAction,
  joinVideoSessionAction,
} from "../../../Store/SagaActions/InitiateVideoCallSagaActions";
import AppFooter from "../../Common/AppFooter";
import Reschedule from "../Reschedule/Reschedule_stage";
import { Text } from "../../Language/Language";
import Header from "../../../Layout/Header";
import H4 from "../../../Components/Elements/H4";
import Instructions from "../../../Components/InstructionsCmp/Instructions";
import CleintNames from "../../../Constants/ClientNames";
import { actionGetVcipDetails } from "../../../Store/SagaActions/GetVcipDetailsSagaActions";
import moment from "moment";

const browser = detect();
var updateTokenInterval = null;

const TokenNumber = () => {
  const [InitiateVCFQ, setInitiateVCFQ] = useState({});
  const [isRescheduled, setIsRescheduled] = useState(false);
  const [intervalConferenceId, setIntervalConferenceId] = useState(null);
  const [timer, setTimer] = useState(null);
  const [isAPIProcessing, setIsAPIProcessing] = useState(false);
  const [isopen, setIsopen] = useState(false);
  const [vcipStatus, setVcipStatus] = useState("");

  const dispatch = useDispatch();
  const navigate = useNavigate();

  // var intervalConferenceId;
  const vcipkeyData = sessionStorage.getItem("vcipkey");
  const langidData = sessionStorage.getItem("langid");
  const userTokendata = sessionStorage.getItem("UserTokendata");

  const cssProperties = useSelector((state) => state.HomeReducer.cssProperties);

  useEffect(() => {
    getVcipDetails();
    if (userTokendata) {
      getUpdatedTokenDetailsEvent();
    } else {
      initiateVideoConference();
    }
    return () => {
      // clearInitiateConference();
      clearInterval(updateTokenInterval);
      setIntervalConferenceId(null);
      sessionStorage.removeItem("UserTokendata");
    };
  }, []);

  const getVcipDetails = () => {
    const refId = sessionStorage.getItem("vcipref");
    const model = {
      vcipref: refId,
    };
    dispatch(
      actionGetVcipDetails({ model: model, callback: getVcipDetailsData })
    );
  };

  const getVcipDetailsData = (data) => {
    setVcipStatus(data);
  };

  useEffect(() => {
    if (
      InitiateVCFQ?.tokennumber === "0" ||
      isRescheduled ||
      InitiateVCFQ?.isscheduled === "1"
    ) {
      clearInitiateConference();
    }
    if (InitiateVCFQ?.isscheduled === "1" || isRescheduled) {
      // SEND USER TO SCHEDULE 
      navigate(RouteNames.RESCHEDULE, { replace: true, state: { InitiateVCFQ: InitiateVCFQ } });
    }
    if (InitiateVCFQ?.respcode === "425") {
      // toast(InitiateVCFQ?.respdesc);
      clearInterval(updateTokenInterval);
      sessionStorage.removeItem("UserTokendata");
      sessionStorage.setItem("tokenSessionExpire", "SessionExpire");
      setTimeout(() => {
        navigate(RouteNames.KYC_PROCESS_COMPLETED, { replace: true });
      }, 2000);
    }
  }, [InitiateVCFQ, isRescheduled]);

  const initiateVideoConference = () => {
    const model = {
      vcipkey: vcipkeyData,
      langid: langidData,
    };
    dispatch(
      actionGetInitiateConferenceQueueSagaAction({
        model: model,
        callback: getInitiateVCFQData,
      })
    );
  };

  const clearInitiateConference = () => {
    clearInterval(updateTokenInterval);
    setIntervalConferenceId(null);
  };

  const getInitiateVCFQData = (data) => {
    // if(data.respcode === "200"){
    //   console.log("data is",data)
    //   sessionStorage.setItem("videoUrl",data?.videoconfsessionurl)
    // }
    setInitiateVCFQ(data);
    if (timer === null || timer !== data?.tokenwaittimer) {
      setTimer(parseInt(data?.tokenwaittimer));
    }
    if (
      parseInt(data?.tokennumber) >= 0 &&
      data?.isscheduled === "0" &&
      data?.respcode !== "425"
    ) {
      getUpdatedTokenDetailsEvent();
    } else {
      // if (data?.tokennumber === '-1' || data?.isscheduled === '1') {
      //     setIsRescheduled(true);
      // }
      // if (intervalConferenceId) {
      //     clearInterval(intervalConferenceId)
      // }
      clearInitiateConference();
      setIntervalConferenceId(null);
    }
  };

  const getUpdatedTokenData = (data) => {
    if (data?.respcode === "425") {
      clearInterval(updateTokenInterval);
      sessionStorage.removeItem("UserTokendata");
    }
    setInitiateVCFQ(data);
    const isDataAvailable = Object.values(InitiateVCFQ).length;
    if (
      (isDataAvailable === 0 && data?.respcode === "200") ||
      InitiateVCFQ?.tokennumber !== data?.tokennumber
    ) {
      sessionStorage.setItem("UserTokendata", JSON.stringify(data));
    }
    if (timer === null || timer !== data?.tokenwaittimer) {
      setTimer(parseInt(data?.tokenwaittimer));
    }
    if (data?.tokennumber === "0") {
      if (updateTokenInterval) {
        clearInterval(updateTokenInterval);
      }
    }
  };

  const getUpdatedTokenDetailsEvent = () => {
    // let intervalId = setInterval(() => {
    updateTokenInterval = setInterval(() => {
      // if (intervalConferenceId === "" || intervalConferenceId === null || intervalConferenceId === undefined) {
      //   setIntervalConferenceId(intervalId);
      // }
      const model = {
        vcipkey: vcipkeyData,
        langid: langidData,
      };
      dispatch(
        actionGetUpdatedTokenSagaAction({
          model: model,
          callback: getUpdatedTokenData,
        })
      );
    }, 1000);
  };



  const bookslot = async () => {
    // navigate(RouteNames.RESCHEDULE)
    // clearInitiateConference();
    await clearInterval(updateTokenInterval);
    await setIntervalConferenceId(null);
    await setIsRescheduled(true);
  };

  const cancelledSchedule = (data) => {
    setIsRescheduled(false);
    initiateVideoConference();
  };

  const joinVideoSession = () => {
    setIsAPIProcessing(false);
    if (intervalConferenceId) {
      clearInterval(intervalConferenceId);
    }
    sessionStorage.setItem("videoconfsessionid",InitiateVCFQ?.videoconfsessionid);
    setTimeout(() => {
      navigate("/session/" + InitiateVCFQ?.videoconfsessionid);
    }, 1000);
  };

  // const joinVideoSession = () => {
  //   setIsAPIProcessing(true);
  //   const location = sessionStorage.getItem("location");
  //   const geolocation = sessionStorage.getItem("geolocation");
  //   const ip = sessionStorage.getItem("ip");
  //   const connection =
  //     navigator.connection ||
  //     navigator.mozConnection ||
  //     navigator.webkitConnection;
  //   let outcoming = "";
  //   if (connection) {
  //     outcoming =
  //       connection.rtt +
  //       " " +
  //       (connection.effectiveType === "4g"
  //         ? connection.effectiveType
  //         : "Low Speed");
  //   }
  //   const model = {
  //     vcipkey: vcipkeyData,
  //     custdetails: {
  //       ip: ip,
  //       location: location,
  //       geolocation: geolocation,
  //       nw_incoming: connection ? connection.downlink : "",
  //       nw_outgoing: outcoming,
  //       videoresolution: `${window.screen.width} x ${window.screen.height}`,
  //       os: browser?.os,
  //     },
  //     agentdetails: {
  //       ip: "***********",
  //       location: "",
  //       geolocation: "",
  //       nw_incoming: "",
  //       nw_outgoing: "",
  //       videoresolution: "",
  //     },
  //   };
  //   dispatch(
  //     joinVideoSessionAction({ model: model, callback: joinVideoSessionData })
  //   );
  // };

  // const joinVideoSessionData = (data) => {
  //   setIsAPIProcessing(false);
  //   if (intervalConferenceId) {
  //     clearInterval(intervalConferenceId);
  //   }
  //   sessionStorage.setItem(
  //     "videoconfsessionid",
  //     InitiateVCFQ?.videoconfsessionid
  //   );
  //   // navigate(RouteNames.INITIATE_VIDEO_CALL + InitiateVCFQ?.videoconfsessionid);
  //   navigate("/session/" + InitiateVCFQ?.videoconfsessionid);
  // };

  const movetovideocheckpage = () => {
    setIsopen(true);
  };

  const clientName = useSelector((state) => state.HomeReducer.clientName);

  const isClientTideAccess =
    clientName === CleintNames?.TIDEDEV ||
    clientName === CleintNames?.TIDEQA ||
    clientName === CleintNames?.TIDE
      ? true
      : false;

  let starttime;
  let endtime;
  if(vcipStatus){
    starttime = vcipStatus.vkyctime.split("T")[0]
    endtime = vcipStatus.vkyctime.split("T")[1]
  }
  
  return (
    <>
      {vcipStatus?.isdovkyc == "0" &&
      !isRescheduled &&
      InitiateVCFQ?.isscheduled === "0" ? (
        <>
          <Header
            title={"Initiating a video call"}
            navigate={() => navigate(-1)}
            isClientTideAccess={isClientTideAccess}
            hideHeader={false}
          />
          <article className="app-body">
            <div className="agentunavailableimg">
              <img src="images/agentunavaailable.png" />
            </div>
            <div className="agentunavailabletxt"><Text tid="agents_unavailable"/></div>
            <div className="agentunavailableinfo">
              <Text tid="agents_currently_unavailable_Please_try_again_between"/> <span style={{color: "rgb(0, 13, 46)",fontWeight: "bold"}}>{starttime}</span> to <span style={{color: "rgb(0, 13, 46)",fontWeight: "bold"}}>{endtime}</span> 
              <Text tid="or_Book_a_slot_for_your_Video_KYC" />
            </div>
            <div className="joinnowbtntide" style={{paddingBottom : "4%" ,backgroundColor: isClientTideAccess ? "#F1F2F3" : "#FFFFFFF2"}}>
              <AppFooter
                btnName={<Text tid="book_slot"/>}
                // isJoinCall={InitiateVCFQ?.tokennumber === "0" ? true : false}
                // isDisabled={
                //   InitiateVCFQ?.enablebookslot !== "1" ||
                //   InitiateVCFQ?.isscheduled === "1" ||
                //   // !updateTokenInterval ||
                //   isAPIProcessing
                //     ? true
                //     : false
                // }
                navigate={
                  InitiateVCFQ?.enablebookslot !== "0" ||
                  InitiateVCFQ?.isscheduled === "1"
                    ? null
                    : bookslot
                }
                joinVideoSession={
                  InitiateVCFQ?.tokennumber === "0"
                    ? movetovideocheckpage
                    : null
                  // InitiateVCFQ?.tokennumber === "0" ? joinVideoSession : null
                }
              />
            </div>
          </article>
        </>
      ) : (
        <>
          {InitiateVCFQ?.isscheduled === "0" && !isRescheduled ? (
            <>
              <Header
                title={isClientTideAccess ? " " : <Text tid="title_token" />}
                navigate={() => navigate(-1)}
                isClientTideAccess={isClientTideAccess}
                hideHeader={false}
              />
              <article className="app-body">
                <div className="tidetokenpageadjust">
                  <TokenNumberCmp
                    InitiateVCFQ={InitiateVCFQ}
                    timer={timer}
                    isClientTideAccess={isClientTideAccess}
                  />
                  {!isClientTideAccess && (
                    <AppFooter
                      btnName={<Text tid="book_slot"/>}
                      isJoinCall={
                        InitiateVCFQ?.tokennumber === "0" ? true : false
                      }
                      isDisabled={
                        InitiateVCFQ?.enablebookslot !== "1" ||
                        InitiateVCFQ?.isscheduled === "1" ||
                        // !updateTokenInterval ||
                        isAPIProcessing
                          ? true
                          : false
                      }
                      navigate={
                        InitiateVCFQ?.enablebookslot !== "1" ||
                        InitiateVCFQ?.isscheduled === "1"
                          ? null
                          : bookslot
                      }
                      joinVideoSession={
                        InitiateVCFQ?.tokennumber === "0"
                          ? movetovideocheckpage
                          : null
                        // InitiateVCFQ?.tokennumber === "0" ? joinVideoSession : null
                      }
                    />
                  )}
                  <div className="joinnowbtntide" style={{backgroundColor: isClientTideAccess ? "#F1F2F3" : "#FFFFFFF2",
                paddingBottom:isClientTideAccess ? "8%" : ""}}>
                    {isClientTideAccess && (
                      <>
                        <AppFooter
                          btnName={"Join Now"}
                          isJoinCall={
                            InitiateVCFQ?.tokennumber === "0" ? true : false
                          }
                          isClientTideAccess={isClientTideAccess}
                          isDisabled={
                            // InitiateVCFQ?.enablebookslot !== "1" ||
                            // InitiateVCFQ?.isscheduled === "1" ||
                            // // !updateTokenInterval ||
                            // isAPIProcessing
                            InitiateVCFQ?.tokennumber !== "0"
                              ? true
                              : false 
                          }
                          // navigate={
                          //   InitiateVCFQ?.enablebookslot !== "1" ||
                          //   InitiateVCFQ?.isscheduled === "1"
                          //     ? null
                          //     : bookslot
                          // }
                          joinVideoSession={
                            InitiateVCFQ?.tokennumber === "0"
                              ? movetovideocheckpage
                              : null
                            // InitiateVCFQ?.tokennumber === "0" ? joinVideoSession : null
                          }
                        />
                        <button
                          className="btn "
                          type="button"
                          style={{
                            background: "#1929D6",
                            borderRadius: "256px",
                            height: "44px",
                            width:"99%",
                            border:"none",
                            fontWeight: "500",
                            fontSize: "16px",
                            lineHeight: "20px",
                            color: "#FFFFFF"
                          }}
                          disabled={
                            // InitiateVCFQ?.enablebookslot !== "1" ||
                            // InitiateVCFQ?.isscheduled === "1" ||
                            // // !updateTokenInterval ||
                            // isAPIProcessing
                            InitiateVCFQ?.tokennumber === "0"
                              ? true
                              : false 
                          }
                          onClick={
                            bookslot
                          }
                          isClientTideAccess={isClientTideAccess}
                        >
                          <Text tid="book_slot_for_later"/>
                        </button>
                      </>
                    )}
                  </div>
                </div>
                {!isClientTideAccess && (
                  <H4
                    className="title mb-2"
                    title={"Note:"}
                    color={cssProperties?.pagetitle_heading?.font_color}
                    fontSize={cssProperties?.pagetitle_heading?.font_size}
                  />
                )}

                {!isClientTideAccess && <Instructions />}
              </article>
            </>
          ) : null}
        </>
      )}

      {isopen && (
        <Micvideocheckmodel
          setIsopen={setIsopen}
          cssProperties={cssProperties}
          RouteNames={RouteNames}
          isClientTideAccess={isClientTideAccess}
          joinVideoSessionreq={joinVideoSession}
          id={InitiateVCFQ?.videoconfsessionid}
        />
      )}
      {(InitiateVCFQ?.isscheduled === "1" || isRescheduled) && (
        <Reschedule
          InitiateVCFQ={InitiateVCFQ}
          isRescheduled={isRescheduled}
          setIsRescheduled={setIsRescheduled}
          cancelledSchedule={cancelledSchedule}
          joinVideoSession={joinVideoSession}
          isClientTideAccess={isClientTideAccess}
          id={InitiateVCFQ?.videoconfsessionid}
        />
      )}
    </>
  );
};

export default TokenNumber;
