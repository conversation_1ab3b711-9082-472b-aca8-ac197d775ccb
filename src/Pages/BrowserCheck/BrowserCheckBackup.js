import React, { Component, useState } from "react";
import { PulseLoader } from "react-spinners";
import { detect } from "detect-browser";
import toast from "react-hot-toast";
import { Text } from "../Language/Language";
const browser = detect();
const Id = sessionStorage.getItem("link");
const user = sessionStorage.getItem("user");

export class BrowserCheck extends Component {
  supportDevices = ["Windows", "Linux", "iPhone", "iPad", "Android"];
  supportedBrowsers = [
    { name: "Chrome", version: 65 },
    { name: "Safari", version: 12 },
  ];
  module = {
    options: [],
    header: [
      navigator.platform,
      navigator.userAgent,
      navigator.appVersion,
      navigator.vendor,
      window.opera,
    ],
    dataos: [
      { name: "Windows Phone", value: "Windows Phone", version: "OS" },
      { name: "Windows", value: "Win", version: "NT" },
      { name: "iPhone", value: "iPhone", version: "OS" },
      { name: "iPad", value: "iPad", version: "OS" },
      { name: "Kindle", value: "Silk", version: "Silk" },
      { name: "Android", value: "Android", version: "Android" },
      { name: "PlayBook", value: "PlayBook", version: "OS" },
      { name: "BlackBerry", value: "BlackBerry", version: "/" },
      { name: "Macintosh", value: "Mac", version: "OS X" },
      { name: "Linux", value: "Linux", version: "rv" },
      { name: "Palm", value: "Palm", version: "PalmOS" },
    ],
    databrowser: [
      { name: "Chrome", value: "Chrome", version: "Chrome" },
      { name: "Firefox", value: "Firefox", version: "Firefox" },
      { name: "Safari", value: "Safari", version: "Version" },
      { name: "Internet Explorer", value: "MSIE", version: "MSIE" },
      { name: "Opera", value: "Opera", version: "Opera" },
      { name: "BlackBerry", value: "CLDC", version: "CLDC" },
      { name: "Mozilla", value: "Mozilla", version: "Mozilla" },
    ],
    init: function () {
      var agent = this.header.join(" "),
        os = this.matchItem(agent, this.dataos),
        browser = this.matchItem(agent, this.databrowser);

      return { os: os, browser: browser };
    },
    matchItem: function (string, data) {
      var i = 0,
        j = 0,
        regex,
        regexv,
        match,
        matches,
        version;

      for (i = 0; i < data.length; i += 1) {
        regex = new RegExp(data[i].value, "i");
        match = regex.test(string);
        if (match) {
          regexv = new RegExp(data[i].version + "[- /:;]([\\d._]+)", "i");
          matches = string.match(regexv);
          version = "";
          if (matches) {
            if (matches[1]) {
              matches = matches[1];
            }
          }
          if (matches) {
            matches = matches.split(/[._]+/);
            for (j = 0; j < matches.length; j += 1) {
              if (j === 0) {
                version += matches[j] + ".";
              } else {
                version += matches[j];
              }
            }
          } else {
            version = "0";
          }
          return {
            name: data[i].name,
            version: parseFloat(version),
          };
        }
      }
      return { name: "unknown", version: 0 };
    },
  };

  state = {
    isCheckerStatus: false,
    browserCheck: false,
    detectDeviceDetails: {},
  };

  async componentDidMount() {
    const detectDeviceDetails = await this.module.init();
    const findSupportedDevice = await this.supportDevices.filter(
      (item) => item === detectDeviceDetails?.os?.name
    );
    const findVesionValid = await this.supportedBrowsers.filter(
      (item) =>
        item?.name === detectDeviceDetails?.browser?.name &&
        item?.version <= detectDeviceDetails?.browser?.version
    );
    let isBrowserValid = false;
    if (findSupportedDevice?.length > 0 && findVesionValid?.length > 0) {
      isBrowserValid = true;
    }
    setTimeout(() => {
      this.setState({
        isCheckerStatus: true,
        browserCheck: isBrowserValid,
        detectDeviceDetails: detectDeviceDetails,
      });
    }, 1000);
  }

  override = {
    display: "block",
    borderColor: "red",
  };

  render() {
    const detectDeviceDetails = this.state.detectDeviceDetails;
    const userDetails = JSON.parse(user);
    return (
      <>
        {/* {!this.state.isCheckerStatus ? ( */}
        {!browser ? (
          <span className="d-flex align-items-end w-100">
            {" "}
            <Text tid="checking_browser_details"/>
            <PulseLoader
              color={"red"}
              loading={true}
              cssOverride={this.override}
              size={4}
            />
          </span>
        ) : (
          <div className="cstmr-brwsr-dtls-mn">
            <div className="cstmr-brwsr-dtls">
              <h2 className="cstmr-brwsr-dtls-ttl">
                <Text tid="hi"/> {userDetails?.cust_name}, <Text tid="you_are_using"/> {browser?.name} {browser?.version} <Text tid="on"/>{" "}
                {browser?.os}{" "}
              </h2>
              {/* //repeat same */}
              {/* <h2 className="cstmr-brwsr-dtls-ttl">
                Hi..., You are using {detectDeviceDetails?.browser?.name}{" "}
                {detectDeviceDetails?.browser?.version} on{" "}
                {detectDeviceDetails?.os?.name}{" "}
                {detectDeviceDetails?.os?.version}
              </h2> */}
              {browser ? (
                <>
                  <p className="cstmr-brwsr-dtls-sbttl">
                    <Text tid="this_browser_and_device_combination_does_not_support_video_calling" />
                    <br />
                    <Text tid="please_copy_the_link_and_open_this_link_will_experire_in_10_minutes"/>
                    <br />
                  </p>
                  <p className="cstmr-brwsr-dtls-sbttl">
                    {/* <span className="text-primary">Copy Link</span> */}
                    <button
                      className="themeBtn btn-primary"
                      style={{height: 32}}
                      onClick={() => {
                        navigator?.clipboard?.writeText(`${Id}`);
                        toast.success("Copied", { position: "bottom-center"});
                      }}
                    >
                      <Text tid="copy_link"/>
                    </button>
                  </p>

                  <div className="cstmr-brwsr-dtls-bx">
                    <div className="cstmr-brwsr-dtls-bx-img">
                      <img src alt="" />
                    </div>
                    <div className="cstmr-brwsr-dtls-bx-cntnt">
                      <h3 className="cstmr-brwsr-dtls-bx-ttl"><Text tid="for_android"/></h3>
                      <p className="cstmr-brwsr-dtls-bx-txt">
                        <Text tid="use"/>: <span><Text tid="google_chrome_V70"/></span>
                      </p>
                    </div>
                  </div>
                  <div className="cstmr-brwsr-dtls-bx">
                    <div className="cstmr-brwsr-dtls-bx-img">
                      <img src alt="" />
                    </div>
                    <div className="cstmr-brwsr-dtls-bx-cntnt">
                      <h3 className="cstmr-brwsr-dtls-bx-ttl"><Text tid="for_iPhone"/></h3>
                      <p className="cstmr-brwsr-dtls-bx-txt">
                      <Text tid="use"/>: <span><Text tid="safari_V12"/></span>
                      </p>
                      <p className="cstmr-brwsr-dtls-bx-txt">
                      <Text tid="use"/>: <span><Text tid="google_chrome_V70"/></span>
                      </p>
                    </div>
                  </div>
                  {/* <div className="cstmr-brwsr-dtls-bx">
                    <div className="cstmr-brwsr-dtls-bx-img">
                      <img src alt="" />
                    </div>
                    <div className="cstmr-brwsr-dtls-bx-cntnt">
                      <h3 className="cstmr-brwsr-dtls-bx-ttl">
                        For Windows Desktop
                      </h3>
                      <p className="cstmr-brwsr-dtls-bx-txt">
                        Use: <span>Google Chrome V70+</span>
                      </p>
                      <p className="cstmr-brwsr-dtls-bx-txt">
                        Use: <span>Microsoft Edge</span>
                      </p>
                    </div>
                  </div> */}
                  {/* <div className="cstmr-brwsr-dtls-bx">
                    <div className="cstmr-brwsr-dtls-bx-img">
                      <img src alt="" />
                    </div>
                    <div className="cstmr-brwsr-dtls-bx-cntnt">
                      <h3 className="cstmr-brwsr-dtls-bx-ttl">For Mac</h3>
                      <p className="cstmr-brwsr-dtls-bx-txt">
                        Use: <span>Safari V12+</span>
                      </p>
                      <p className="cstmr-brwsr-dtls-bx-txt">
                        Use: <span>Google Chrome V70+</span>
                      </p>
                    </div>
                  </div> */}
                </>
              ) : (
                "Valid Browser"
              )}
            </div>
          </div>
        )}
      </>
    );
  }
}

export default BrowserCheck;
