import React from 'react';
import { useDispatch } from 'react-redux';

export default function PdfViewModal(props) {
    const allowPdfSubmit = () => {
        props?.setShowPdfPopup(false)
        const PdfApprove = {
            streamingKey: "CUSTOMER_APPROVED_PDF_VIEW",
            streamingValue: "Customer Approved PDF View",
            notificationid: "PDF_APPROVE",
            notifications: "Customer Approved PDF View",
        }
        props?.sendMessage(PdfApprove)
    }

    const cancelPdfView = () => {
        props?.setShowPdfPopup(false)
        const data = "Customer Declined PDF View"
        const PdfDecline = {
            streamingKey: "CUSTOMER_DECLINE_PDF_VIEW",
            streamingValue: "Customer Declined PDF View",
            notificationid: "PDF_DECLINE",
            notifications: {data , isPdfDialogVisible: false},
        }
        props?.sendMessage(PdfDecline)
    }

    const viewPdf = () => {
        // Open PDF in a new window or tab
        if (props?.pdfUrl) {
            window.open(props.pdfUrl, '_blank');
        }
    }

    return (
        <div className='modalbackground'>
            <div className='modalbody'>
                <div className='title'>
                    Please review the document
                </div>
                <div className="modalborder"></div>

                {/* PDF View Section */}
                <div style={{ padding: '20px', textAlign: 'center' }}>
                    <p style={{ marginBottom: '15px', fontSize: '14px' }}>
                        Click "View Document" to review the PDF before proceeding.
                    </p>

                    {props?.pdfUrl && (
                        <button
                            onClick={viewPdf}
                            style={{
                                backgroundColor: '#007bff',
                                color: 'white',
                                border: 'none',
                                padding: '10px 20px',
                                borderRadius: '5px',
                                marginBottom: '20px',
                                cursor: 'pointer',
                                fontSize: '14px'
                            }}
                        >
                            View Document
                        </button>
                    )}
                </div>

                <div className="allowoption">
                    <div className="dontallow opacity-80" onClick={cancelPdfView}>
                        Cancel
                    </div>
                    <div className='allow' onClick={allowPdfSubmit}>
                        Submit
                    </div>
                </div>
            </div>
        </div>
    )
}
