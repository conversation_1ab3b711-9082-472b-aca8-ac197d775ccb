.upload-document-process {
    background-color: #ffffff;
    border-radius: 12px;
    font-family: Arial, sans-serif;
    padding: 20px;
    position: relative;
    z-index: 1000;
}

.document-title {
    font-size: 22px;
    margin-top: 30px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.step-indicator {
    color: #6b7280;
    font-size: 14px;
    margin-top: 0;
    margin-bottom: 24px;
}

.document-section {
    margin-bottom: 24px;
}

.document-sections-wrap{
    height: 70vh;
    overflow: auto;
}

.document-type {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
}

.upload-area {
    border: 1px dashed #a0aec0;
    border-radius: 8px;
    padding: 36px;
    text-align: center;
    cursor: pointer;
    color: #3b82f6;
    background-color: #f9fafb;
    transition: background-color 0.2s;
}

.upload-icon-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.supported-format {
    color: #6b7280;
    font-size: 12px;
}

.upload-area:hover {
    background-color: #f0f4f8;
}

.file-input {
    display: none;
}

.uploaded-file {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    position: relative;
}

.file-icon {
    width: 40px;
    height: 40px;
    background-color: #e5e7eb;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 12px;
}

.optional-label {
    font-weight: normal;
    font-size: 0.9em;
    color: #666;
}

.required-mark {
    color: #e53e3e;
    margin-left: 4px;
}

.file-icon svg {
    width: 24px;
    height: 24px;
}

.file-details {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.file-name-size {
    display: flex;
    gap: 8px;
    align-items: center;
}

.file-name {
    font-size: 14px;
    color: #111827;
    margin-bottom: 4px;
}

.file-size {
    font-size: 12px;
    color: #6b7280;
}

.view-button {
    font-size: 12px;
    color: #3b82f6;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    text-align: left;
    margin-top: 4px;
}

.view-button:hover {
    text-decoration: underline;
}

.save-button {
    width: 100%;
    padding: 14px;
    background-color: #3366cc;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-top: 20px;
}

.save-button:hover {
    background-color: #2855aa;
}

.remove-button {
    border: none;
    background: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    background-color: #e5e7eb;
}

.remove-button:hover {
    background-color: #e5e7eb;
}

.remove-button svg {
    color: #6b7280;
}

.update-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    margin-left: 8px;
    border-radius: 50%;
    background-color: #e5e7eb;
}

/* Popup styles */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.popup-content {
    background-color: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.popup-content h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.popup-content p {
    margin: 0 0 24px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.popup-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.popup-button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.confirm-button {
    background-color: #3366cc;
    color: white;
}

.confirm-button:hover {
    background-color: #2855aa;
}

.cancel-button {
    background-color: #e5e7eb;
    color: #374151;
}

.cancel-button:hover {
    background-color: #d1d5db;
    
}