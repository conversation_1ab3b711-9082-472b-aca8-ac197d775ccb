import { MdUploadFile } from "react-icons/md";
import { MdOutlineDeleteOutline } from "react-icons/md";

const DocumentSection = ({
    documentType,
    title,
    document,
    inputRefsContainer,
    onFileSelect,
    onViewDocument,
    onRemoveDocument
}) => {
    const handleClick = () => {
        if (!document.uploaded && inputRefsContainer.current[documentType]) {
            inputRefsContainer.current[documentType].click();
        }
    };

    return (
        <div className="document-section">
            <div className="document-type">
                {title}
            </div>
            {!document.uploaded ? (
                <div className="upload-area" onClick={handleClick}>
                    <input
                        type="file"
                        className="file-input"
                        ref={el => inputRefsContainer.current[documentType] = el}
                        onChange={e => onFileSelect(documentType, e)}
                        accept=".pdf,.jpg,.jpeg,.png"
                    />
                    <div className="upload-icon-text">
                        <MdUploadFile size={32} />
                        <span>Click to upload</span>
                        <span className="supported-format">Supported formats: PDF, JPG, PNG</span>
                    </div>
                </div>
            ) : (
                <div className="uploaded-file">
                    <div className="file-icon">
                        <MdUploadFile />
                    </div>
                    <div className="file-details">
                        <div className="file-name-size">
                            <span className="file-name">{document.file.name}</span>
                            <span className="file-size">{document.file.size}</span>
                        </div>
                        <button className="view-button" onClick={() => onViewDocument(documentType)}>
                            View Document
                        </button>
                    </div>
                    <button
                        className="remove-button"
                        onClick={() => onRemoveDocument(documentType)}
                    >
                        <MdOutlineDeleteOutline size={24} color="red" />
                    </button>
                </div>
            )}
        </div>
    );
};

export default DocumentSection;