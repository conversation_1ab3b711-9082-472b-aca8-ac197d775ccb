import React from 'react';

const UpdateDocumentPopup = ({ 
  isVisible, 
  documentName, 
  onConfirm, 
  onCancel 
}) => {
  if (!isVisible) return null;

  return (
    <div className="popup-overlay">
      <div className="popup-content">
        <h3>Update Document</h3>
        <p>
          Do you want to update the <strong>{documentName}</strong> and add a new one?
        </p>
        <div className="popup-buttons">
          <button 
            className="popup-button confirm-button" 
            onClick={onConfirm}
          >
            Update
          </button>
          <button 
            className="popup-button cancel-button" 
            onClick={onCancel}
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpdateDocumentPopup;