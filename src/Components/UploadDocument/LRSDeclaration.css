.lrs-declaration-container {
    background-color: #ffffff;
    border-radius: 12px;
    font-family: Arial, sans-serif;
    margin: 20px;
    padding-bottom: 80px;
    min-height: calc(100vh - 40px);
    position: relative;
    z-index: 1000;
}

.declaration-title {
    font-size: 22px;
    margin-top: 15px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    line-height: 1.4;
}

.step-indicator {
    color: #6b7280;
    font-size: 14px;
    margin-top: 0;
    margin-bottom: 15px;
}

.declaration-text {
    font-size: 18px;
    line-height: 1.5;
    margin-bottom: 15px;
    color: #333;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.radio-group label {
    display: flex;
    align-items: center; 
    gap: 8px;
    cursor: pointer;
    font-size: 18px;
}

.radio-group input[type="radio"] {
    width: 15px;
    height: 15px;
    accent-color: #0066cc;
    margin: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.half-width {
    flex: 1;
}

label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.save-button {
    width: 100%;
    padding: 14px;
    background-color: #0066cc;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-top: 0px
}

.save-button:hover {
    background-color: #2855aa;
}

.save-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.error-message {
    color: #d32f2f;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

.view-only input[type="radio"] {
    pointer-events: none;
}

/* .submit-button {
    width: calc(100% - 40px);
    padding: 14px;
    background-color: #0066cc;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    z-index: 9999;
    margin: 0;
}

.submit-button:hover:not(:disabled) {
    background-color: #2855aa;
}

.submit-button:disabled {
    background-color: #a8bce0;
    cursor: not-allowed;
} */