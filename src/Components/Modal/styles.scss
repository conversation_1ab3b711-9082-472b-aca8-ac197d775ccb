$modal-z-index: 99999;
$modal-overlay-color: rgba(0, 0, 0, 0.5);
$modal-background: #fff;
$modal-max-width: 500px;
$modal-padding: 20px;
$modal-radius: 8px;
$modal-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
$modal-border-bottom: 1px solid #ddd;

$button-margin-left: 10px;
$button-padding: 10px 20px;
$button-radius: 5px;
$button-primary-background: #007bff;
$button-primary-color: white;
$button-secondary-background: #6c757d;
$button-secondary-color: white;

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $modal-overlay-color;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: $modal-z-index;
}

.pep-info-btn {
  position: fixed;
  bottom: 10px;
  width: 92%;
}

.modal-container {
  background-color: $modal-background;
  padding: $modal-padding;
  border-radius: $modal-radius;
  max-width: $modal-max-width;
  width: 90%;
  box-shadow: $modal-box-shadow;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: $modal-border-bottom;
  padding-bottom: 10px;

  h2 {
    margin: 0;
  }

  .close-icon {
    cursor: pointer;
    font-size: 24px;
  }
}

.modal-body {
  margin: 20px 0;
}

.pep-section:first-child {
  padding-bottom: 10px;
  border-bottom: 2px solid #9e999987;
  margin-bottom: 10px;
}

.pep-modal-wrapper {
  padding: 0 4%;
  box-sizing: border-box;
  overflow-y: scroll;
  height: 70vh;
  margin: 4% 0 18% 0;
}

.pep-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-top: 10px;
  list-style: none;

  li {
    margin: 5px;
    padding: 5px 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
    flex: none;
  }
}

.info-icon {
  margin-left: 5px;
  font-size: 18px;
  cursor: pointer;
//   color: $secondary-color;

  &:hover {
    // color: $primary-color;
  }
}

// .pep-list {
//     display: flex;
//     flex-wrap: wrap;
//     padding: 0;
//     list-style: none;

//     li {
//       margin: 5px 10px;
//       padding: 0;
//       background: none;
//       border-radius: 0;
//       flex: none;
//     }
//   }

.modal-footer {
  display: flex;
  justify-content: flex-end;

  .button {
    margin-left: $button-margin-left;
    padding: $button-padding;
    border: none;
    border-radius: $button-radius;
    cursor: pointer;

    &.primary {
      background-color: $button-primary-background;
      color: $button-primary-color;
    }

    &.secondary {
      background-color: $button-secondary-background;
    //   color: $button-secondary-color;
    }
  }
}

@media (max-width: 768px) {
  .modal-container {
    width: 95%;
  }
}
