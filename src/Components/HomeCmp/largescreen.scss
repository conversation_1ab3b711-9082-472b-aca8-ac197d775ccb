.root{
    // width: 100vw;
    height: 100vh;
    background: #9FBAD5 0% 0% no-repeat padding-box;
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .root-main{
        // position: absolute;
        // top: 70px;
        // left: 154px;
        // margin-top: 70px;
        // margin-left: 154px;
        // width: 60%;
        // height: 539px;
        background: #FFFFFF 0% 0% no-repeat padding-box;
        box-shadow: 0px 0px 4px #00000029;
        border-radius: 12px;
        opacity: 1;
        display: flex;
        margin: 40px;
        // flex-direction: ;
        .videokycheader{
            // width: 58%;
            // height: 539px;
            background: #254282 0% 0% no-repeat padding-box;
            border-radius: 12px 0px 0px 12px;
            opacity: 1;
            // background-image: url('/khyaal/images/Group 1537.svg');
            background-position: center;
            background-repeat: no-repeat;
            // filter:alpha(opacity=60);
            // background-size: cover;
            padding-left: 60px;
            padding-top: 30px;
            // padding-right: 20px;
            position: relative;
            z-index: 99999;
            
            .videokyctext{
                // width: 18.3%;
                // height: 49px;
                text-align: left;
                font: normal normal 600 35px/53px Poppins;
                font-family: 'Poppins', sans-serif;
                letter-spacing: 0px;
                color: black;
                opacity: 1;
            }
            .QR-note{
                margin-top: 20px;
                // width: 46px;
                // height: 49px;
                text-align: left;
                font: normal normal normal 18px/27px Poppins;
                font-family: 'Poppins', sans-serif;
                letter-spacing: 0px;
                color: black;
                opacity: 1;
                // margin-left: -40px;
            }
            .list{
                margin-top: 12px;
                width: 90%;
                // height: 25px;
                margin-left: 15px;
                text-align: left;
                font: normal normal normal 16px/25px Poppins;
                font-family: 'Poppins', sans-serif;
                letter-spacing: 0px;
                color: black;
                opacity: 1;
                line-height: 25px;
                font-size: 18px;
                // padding-right: 10px;
            }
            .videokycimg{
                // margin-top: 200px;
                display: flex;
                justify-content: center;
                margin-bottom: 20px;
                margin-right: 30px;
            }
        }
        .qrheader{
            opacity: 1;
            margin-top: 35px;
            margin-left:60px;
            margin-right: 60px;
            // background-image: url("./../images/QR-mobile.svg");
            background-repeat: no-repeat;
            // background-position: center;
            // width: 34%;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            padding: 30px;
            .qrtitle{
                // width: 143px;
                height: 28px;
                text-align: left;
                font: normal normal 600 20px/30px Poppins;
                font-family: 'Poppins', sans-serif;
                letter-spacing: 0px;
                color: #254282;
                opacity: 1;
                margin-top:73px;
            }
            .qr{
                opacity: 1;
                margin-top: 23px;
            }
            .qrinfo{
                width: 227px;
                height: 36px;
                text-align: center;
                font: normal normal normal 12px/18px Poppins;
                font-family: 'Poppins', sans-serif;
                letter-spacing: 0px;
                color: #333333;
                opacity: 1;
                margin-top: 21px;
            }
            .or{
                margin-top: 15px;
                // width: 12px;
                height: 17px;
                text-align: center;
                font: normal normal normal 12px/18px Poppins;
                letter-spacing: 0px;
                color: #999999;
                font-family: 'Poppins', sans-serif;
            }
            .copybutton{
                width: 113px;
                height: 38px;
                // background: #254282 0% 0% no-repeat padding-box;
                box-shadow: 0px 0px 3px #0000003D;
                border-radius: 6px;
                opacity: 1;
                margin-top: 12px;
                border: none;
                cursor: pointer;
               &:hover{
                font-weight: 900;
               }
                span{
                    // width: 68px;
                    height: 20px;
                    font: normal normal medium 14px/21px Poppins;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                    opacity: 1;
                    font-family: 'Poppins', sans-serif;
                }
            }
        }
        
    }
}