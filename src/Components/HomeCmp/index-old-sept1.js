import React from "react";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import H1 from "../Elements/H1";
import H4 from "../Elements/H4";
import P from "../Elements/P";
import Instructions from "../InstructionsCmp/Instructions";
import "./clock.css";
import QR from "./QR";
import { Text } from "../../Pages/Language/Language";

const Id = sessionStorage.getItem("link");

const HomeCmp = (props) => {
  const cssProperties = useSelector((state) => state.HomeReducer.cssProperties);
  return (
    <section
      className={"app-start" + (props?.isLargeDevice ? " l-app-start" : "")}
    >
      {/* <h1 className="main-heading">Video KYC</h1> */}
      <div className="row justify-content-center align-content-center bgWhite">
        <div className="col">
          <H1
            title={<Text tid="video_kyc"/>}
            color={cssProperties?.pagetitle_heading?.font_color}
            // fontSize={cssProperties?.pagetitle_heading?.font_size}
          />
          {props?.statusTxt && (
            <P
              className="txt text-center"
              txt={props?.customerVcipDetails?.respdesc}
              color={cssProperties?.body?.font_color}
              // fontSize={cssProperties?.body?.font_size}
            />
          )}
          {props?.waitingTimeTxt && (
            <>
              <div className="text-center mb-4">
                <div
                  className="clock"
                  style={{
                    borderColor: cssProperties?.button?.color,
                    margin: "10px auto",
                  }}
                ></div>
                <div style={{ flex: 1, marginLeft: 4 }}>
                  <P
                    className="txt"
                    txt={props?.waitingTimeTxt}
                    color={cssProperties?.body?.font_color}
                  />
                </div>
              </div>
            </>
          )}
          <H4
            className="title mb-2"
            title={"Note:"}
            color={cssProperties?.pagetitle_heading?.font_color}
            fontSize={cssProperties?.pagetitle_heading?.font_size}
          />
          <Instructions />
        </div>
        {props?.isLargeDevice && (
          <div className="text-center w-100 col">
            <img
              src={
                "data:image/png;base64," + props?.customerVcipDetails?.qrimage
              }
              className="w-75"
            />
            <P
              className="txt text-center"
              txt={
                "Please Scan this QR on your mobile and Complete the video KYC Process"
              }
              color={cssProperties?.body?.font_color}
              fontSize={cssProperties?.body?.font_size}
            />
            <p className="txt text-center">OR</p>

            <button
              className="themeBtn btn-primary"
              style={{ height: 32 }}
              onClick={() => {
                navigator?.clipboard?.writeText(`${Id}`);
                toast.success("Copied", { position: "bottom-center"});
              }}
            >
              <Text tid="copy_link" />
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default HomeCmp;
