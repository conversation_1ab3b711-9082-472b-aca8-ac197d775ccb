import toast from "react-hot-toast";
import { put, takeLatest, call } from "redux-saga/effects";
import Axios from "../../Service/axios";
import { actionReqResStatusLoaderSagaAction } from "../SagaActions/CommonSagaActions";
import {
    ACTION_SUBMIT_CUSTOMER_DOCUMENTS_REQ_SAGA_ACTION,
    ACTION_GET_DOCUMENT_LIST_SAGA_ACTION,
    ACTION_FETCH_DOCUMENT_LIST_DATA_SAGA_ACTION,
    ACTION_SUBMIT_LRS_DECLARATION_REQ_SAGA_ACTION
} from "../SagaActions/SagaActionTypes";

// SUBMIT CUSTOMER DOCUMENTS
const submitCustomerDocumentsReq = (model) => {
    const URL = "SubmitCustomerMultiSupportDocs";
    return Axios.post(URL, model).then(res => { return res?.data });
}

function* submitCustomerDocumentsReqSaga(action) {
    yield put(actionReqResStatusLoaderSagaAction(true));
    try {
        const resp = yield call(submitCustomerDocumentsReq, action?.payload?.model);
        if (resp && resp?.respcode === "200") {
            if (action?.payload?.callback) {
                action?.payload?.callback(resp);
            }
        } else {
            if (action?.payload?.callback) {
                action?.payload?.callback(resp);
            }
        }
    } catch (err) {
        if (action?.payload?.callback) {
            action?.payload?.callback(err);
        }
    } finally {
        yield put(actionReqResStatusLoaderSagaAction(false));
    }
}

// GET DOCUMENT LIST
const getDocumentListReq = (model) => {
    const URL = "GetDocumentList";
    return Axios.post(URL, model).then(res => { return res?.data });
}

function* getDocumentListReqSaga(action) {
    yield put(actionReqResStatusLoaderSagaAction(true));
    try {
        const resp = yield call(getDocumentListReq, action?.payload?.model);
        if (resp && resp?.respcode === "200") {
            if (action?.payload?.callback) {
                action?.payload?.callback(resp);
            }
        } else {
            toast.error(resp?.respdesc || "Failed to fetch document list");
        }
    } catch (err) {
        if (err.response) {
            toast.error(err?.response?.data?.errors?.length && err?.response?.data?.errors[0]?.message);
        } else {
            toast.error(err.message);
        }
    } finally {
        yield put(actionReqResStatusLoaderSagaAction(false));
    }
}

const fetchDocumentListDataReq = (model) => {
    const URL = "FetchDocumentListData";
    return Axios.post(URL, model).then(res => { return res?.data });
}

function* fetchDocumentListDataReqSaga(action) {
    yield put(actionReqResStatusLoaderSagaAction(true));
    try {
        const resp = yield call(fetchDocumentListDataReq, action?.payload?.model);
        if (resp && resp?.respcode === "200") {
            if (action?.payload?.callback) {
                action?.payload?.callback(resp);
            }
        } else {
            toast.error(resp?.respdesc || "Failed to fetch document list data");
        }
    } catch (err) {
        if (err.response) {
            toast.error(err?.response?.data?.errors?.length && err?.response?.data?.errors[0]?.message);
        } else {
            toast.error(err.message);
        }
    } finally {
        yield put(actionReqResStatusLoaderSagaAction(false));
    }
}

// SUBMIT LRS DECLARATION

const submitLRSDeclarationReq = (model) => {
    const URL = "SubmitLRSDeclaration";
    return Axios.post(URL, model).then(res => res?.data);
}

function* submitLRSDeclarationReqSaga(action) {
    yield put(actionReqResStatusLoaderSagaAction(true));
    try {
        const resp = yield call(submitLRSDeclarationReq, action?.payload?.model);
        if (resp && resp?.respcode === "200") {
            toast.success("LRS Declaration submitted successfully");
            if (action?.payload?.callback) {
                action?.payload?.callback(resp);
            }
        } else {
            toast.error(resp?.respdesc || "Failed to submit LRS Declaration");
        }
    } catch (err) {
        toast.error(err?.message || "Failed to submit LRS Declaration");
    } finally {
        yield put(actionReqResStatusLoaderSagaAction(false));
    }
}


export default function* DocumentWatcherSaga() {
    yield takeLatest(ACTION_SUBMIT_CUSTOMER_DOCUMENTS_REQ_SAGA_ACTION, submitCustomerDocumentsReqSaga);
    yield takeLatest(ACTION_GET_DOCUMENT_LIST_SAGA_ACTION, getDocumentListReqSaga);
    yield takeLatest(ACTION_FETCH_DOCUMENT_LIST_DATA_SAGA_ACTION, fetchDocumentListDataReqSaga);
    yield takeLatest(ACTION_SUBMIT_LRS_DECLARATION_REQ_SAGA_ACTION, submitLRSDeclarationReqSaga);
}