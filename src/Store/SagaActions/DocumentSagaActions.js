import {
    ACTION_SUBMIT_CUSTOMER_DOCUMENTS_REQ_SAGA_ACTION,
    ACTION_GET_DOCUMENT_LIST_SAGA_ACTION,
    ACTION_FETCH_DOCUMENT_LIST_DATA_SAGA_ACTION
} from "./SagaActionTypes";

export const submitCustomerDocumentsSagaAction = (payload) => {
    return {
        type: ACTION_SUBMIT_CUSTOMER_DOCUMENTS_REQ_SAGA_ACTION,
        payload: payload
    }
}

export const getDocumentListSagaAction = (payload) => {
    return {
        type: ACTION_GET_DOCUMENT_LIST_SAGA_ACTION,
        payload: payload
    }
}

export const fetchDocumentListDataSagaAction = (payload) => {
    return {
        type: ACTION_FETCH_DOCUMENT_LIST_DATA_SAGA_ACTION,
        payload: payload
    }
}

export const submitLRSDeclarationSagaAction = (payload) => {
    return {
        type: "ACTION_SUBMIT_LRS_DECLARATION_REQ_SAGA_ACTION",
        payload: payload
    }
}
