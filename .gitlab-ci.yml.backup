image: node:16.17.0

stages:
  - deploy

deploy:
  stage: deploy
  only:
    - muzzu-dev
  environment:
    name: muzzu-dev-customer
    # url: https://afaaq_website.com

  before_script:
    - npm install --global yarn || true
    # - bash ./script.sh
    # - chmod +x ./script.sh || true

  script:
    - yarn install
    - npm i netlify-cli -f
    - CI=false yarn run build
    - npx netlify-cli deploy --site $CUSTOMER_NETLIFY_SITE_ID --auth $NETLIFY_AUTH_TOKEN --prod
    # - echo $NETLIFY_AUTH_TOKEN && npx netlify-cli deploy --prod -s "$NETLIFY_SITE_ID" --dir build || true
    # - npx netlify deploy --prod --dir ./build --message "site deployed yay" --auth $NETLIFY_AUTH_TOKEN --site $NETLIFY_SITE_ID
    # - npx netlify-cli deploy --site $NETLIFY_SITE_ID --auth $NETLIFY_AUTH_TOKEN --prod
