// {
//     "extends": ["prettier", "react-app", "react-app/jest"],
//     "overrides": [
//       {
//         "files": ["**/*.spec.js", "**/*.spec.jsx"],
//         "env": {
//           "jest": true
//         }
//       }
//     ],
//     "env": {
//       "browser": true,
//       "es2021": true
//     },
//     "rules": {
//       "eqeqeq": 0,
//       "sort-imports": [
//         "error",
//         {
//           "ignoreCase": true,
//           "ignoreDeclarationSort": true,
//           "ignoreMemberSort": true,
//           "memberSyntaxSortOrder": ["none", "all", "multiple", "single"],
//           "allowSeparatedGroups": true
//         }
//       ],
//       "no-shadow": "off",
//       "no-nested-ternary": 0,
//       "import/no-unresolved": 0,
//       "import/no-named-as-default": 0,
//       "no-unused-expressions": 0,
//       "comma-dangle": 0, // not sure why airbnb turned this on. gross!
//       // "no-console": ["error", { "allow": ["warn", "error"] }],
//       "no-console":0,
//       "no-alert": 0,
//       "id-length": 0,
//       "no-script-url": 0,
//       "import/no-extraneous-dependencies": 0,
//       "no-underscore-dangle": 0,
//       "react/jsx-filename-extension": 0,
//       "global-require": 0,
//       "import/newline-after-import": 0,
//       "import/extensions": 0,
//       "prefer-template": 0,
//       "max-len": 0,
//       "react/prefer-stateless-function": 0,
//       "react/forbid-prop-types": "off",
//       "jsx-a11y/href-no-hash": "off",
//       "function-paren-newline": 0,
//       "react/no-typos": 0,
//       "jsx-a11y/anchor-is-valid": 0,
//       "react/default-props-match-prop-types": "off",
//       "arrow-parens": 0,
//       "linebreak-style": 0,
//       "semi": ["error", "always"],
//       "quotes": ["warn", "double"],
//       "camelcase": 1,
//       "no-confusing-arrow": "warn",
//       "object-curly-newline": 1,
//       "implicit-arrow-linebreak": 0,
//       "react/jsx-one-expression-per-line": "off",
//       "react/prop-types": "off",
//       "jsx-a11y/label-has-for": [
//         2,
//         {
//           "required": {
//             "every": ["id"]
//           }
//         }
//       ]
//     },
//     "plugins": ["react", "import", "flowtype"],
//     "settings": {
//       "import/parser": "babel-eslint",
//       "import/resolve": {
//         "moduleDirectory": ["node_modules", "src"]
//       }
//     },
//     "globals": {
//       "__CLIENT__": true,
//       "__SERVER__": true,
//       "beforeAll": true,
//       "afterAll": true
//     }
//   }
  