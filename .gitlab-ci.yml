#if we want to run this pipeline, we need to trigger it in "CICD > Pipleines> Run Pipeline> select branch > Run"
#workflow:
#        rules:
#            - if: '$CI_PIPELINE_SOURCE == "web"'
#              when: always
#   #         - when: never

stages:
    - deploy
    - sonarqube


##sonarqube job will run automatically if an user raise a merge req to LIVEKIT-DEV branch 
sonarqube-scan:
  stage: sonarqube
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  only:
    - merge_requests
    - baranches@LIVEKIT-DEV
  script:
    - sonar-scanner -X -Dsonar.projectKey=phase2-vkyc-customer-master -Dsonar.sources=. -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_LOGIN_KEY

sonarqube-scan_afaaq_sonarQube:
  stage: sonarqube
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  #only:
  #  - merge_requests
  #  - branches@LIVEKIT-DEV
  rules:
  - if: '$CI_COMMIT_BRANCH == "afaaq/sonarQube"'
    when: always
  script:
    - sonar-scanner -X -Dsonar.projectKey=phase2-vkyc-customer-master-afaaq-sonarQube -Dsonar.sources=. -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_LOGIN_KEY_afaaq_sonarQube

sonarqube-scan_customer_sonarQube:
  stage: sonarqube
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  #only:
  #  - merge_requests
  #  - branches@LIVEKIT-DEV
  rules:
  - if: '$CI_COMMIT_BRANCH == "customer-sonarqube"'
    when: always
  script:
    - sonar-scanner -X -Dsonar.projectKey=phase2-vkyc-customer-master-customer-sonarqube -Dsonar.sources=. -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_LOGIN_KEY_customer_sonarqube


deploy_to_lq_dev_livekit:
    #image: napp/docker-aws-cli
    image: senseyeio/node-aws-cli
    stage: deploy
    script:
        #- zip vkyc-customer.zip -r .
        #- apt-get install -y nodejs
        - npm i -f
        #- npm audit fix --force
        - CI=false npm run build:lq_dev_livekit
        - pwd
        - ls -la ./build
        - ls -la ./build/LQ-DEV-LIVEKIT-CUSTOMER
        - cd ./build
        - aws s3 cp ./LQ-DEV-LIVEKIT-CUSTOMER/ s3://$AWS_BUCKET_LQ_DEV/ --recursive
        - aws cloudfront create-invalidation --distribution-id=ED6GDWB4CPAZY --paths "/*"
    only:
        - LIVEKIT-DEV


deploy_to_lq_uat_livekit:
    #image: napp/docker-aws-cli
    image: senseyeio/node-aws-cli
    stage: deploy
    script:
        #- zip vkyc-customer.zip -r .
        #- apt-get install -y nodejs
        - npm i -f
        #- npm audit fix --force
        - CI=false npm run build:lq_uat_livekit
        - pwd
        - ls -la ./build
        - ls -la ./build/LQ-UAT-LIVEKIT-CUSTOMER
        - cd ./build
        - aws s3 cp ./LQ-UAT-LIVEKIT-CUSTOMER/ s3://$AWS_BUCKET_LQ_UAT/ --recursive
        - aws cloudfront create-invalidation --distribution-id=EH9P295A9ZR79 --paths "/*"
    only:
        - LIVEKIT-UAT
    when: manual

# deploy_to_tc_uat:
#     #image: napp/docker-aws-cli
#     image: senseyeio/node-aws-cli
#     stage: deploy
#     script:
#         #- zip vkyc-customer.zip -r .
#         #- apt-get install -y nodejs
#         - npm i -f
#         #- npm audit fix --force
#         - CI=false npm run build:tc_uat
#         - pwd
#         - ls -la ./build
#         - ls -la ./build/TC-UAT-CUSTOMER
#         - cd ./build
#         - aws s3 cp ./TC-UAT-CUSTOMER/ s3://$AWS_BUCKET_TC_UAT/ --recursive
#         - aws cloudfront create-invalidation --distribution-id=E2HVBBWW3DO15Q --paths "/*"
#     only:
#         - LIVEKIT-UAT
#     when: manual

deploy_to_tc_uat_livekit:
    #image: napp/docker-aws-cli
    image: senseyeio/node-aws-cli
    stage: deploy
    script:
        #- zip vkyc-customer.zip -r .
        #- apt-get install -y nodejs
        - npm i -f
        #- npm audit fix --force
        - CI=false npm run build:tc_uat_livekit
        - pwd
        - ls -la ./build
        - ls -la ./build/TC-UAT-LIVEKIT-CUSTOMER
        - cd ./build
        - aws s3 cp ./TC-UAT-LIVEKIT-CUSTOMER/ s3://$AWS_BUCKET_TC_UAT/ --recursive
        - aws cloudfront create-invalidation --distribution-id=E2HVBBWW3DO15Q --paths "/*"
    only:
        - LIVEKIT-UAT
    when: manual

deploy_to_tc_preprod:
    #image: napp/docker-aws-cli
    image: senseyeio/node-aws-cli
    stage: deploy
    script:
        #- zip vkyc-customer.zip -r .
        #- apt-get install -y nodejs
        - npm i -f
        #- npm audit fix --force
        - CI=false npm run build:tc_pre_prod
        - pwd
        - ls -la ./build
        - ls -la ./build/TC-UAT-CUSTOMER
        - cd ./build
        - aws s3 cp ./TC-UAT-CUSTOMER/ s3://$AWS_BUCKET_TC_PREPROD/ --recursive
       # - aws cloudfront create-invalidation --distribution-id=E2HVBBWW3DO15Q --paths "/*"
    only:
        - PRE_PROD
  #  when: manual

deploy_to_tc_prod:
    #image: napp/docker-aws-cli
    image: senseyeio/node-aws-cli
    stage: deploy
    script:
        #- zip vkyc-customer.zip -r .
        #- apt-get install -y nodejs
        - npm i -f
        #- npm audit fix --force
        - CI=false npm run build:tc_prod
        - pwd
        - ls -la ./build
        - ls -la ./build/TC-PROD-CUSTOMER
        - cd ./build
        - aws s3 cp ./TC-PROD-CUSTOMER/ s3://$AWS_BUCKET_TC_PROD/ --recursive
        - aws cloudfront create-invalidation --distribution-id=E1DT0QD11M3LY8 --paths "/*"
    when: manual
    only:
        - PROD


deploy_to_lq_prod:
    #image: napp/docker-aws-cli
    image: senseyeio/node-aws-cli
    stage: deploy
    script:
        #- zip vkyc-customer.zip -r .
        #- apt-get install -y nodejs
        - npm i -f
        #- npm audit fix --force
        - CI=false npm run build:lq_prod
        - pwd
        - ls -la ./build
        - ls -la ./build/LQ-PROD-CUSTOMER
        - cd ./build
        - aws s3 cp ./LQ-PROD-CUSTOMER/ s3://$AWS_BUCKET_LQ_PROD/ --recursive
    when: manual
    only:
        - PROD
